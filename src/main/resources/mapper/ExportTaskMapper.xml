<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joyadata.scc.mapper.ExportTaskMapper">

    <select id="queryDatasourceInfoById" resultType="com.joyadata.scc.model.imports.databases.DatasourceInfoDTO">
        select *
        from business_database.datasource_info
        where datasource_info_id in
        <foreach collection="datasourceIds" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </select>

    <select id="queryDatasourceInfoAuthorizationByDatasourceIds"
            resultType="com.joyadata.scc.model.imports.databases.DatasourceInfoAuthorizationDTO">
        select * from business_database.datasource_info_authorization where product_id=#{productId}
        and project_id=#{projectId}
        and datasource_info_id in
        <foreach collection="datasourceIds" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </select>

    <select id="queryDatasourceInfoAuthorizationOperationByDatasourceIds"
            resultType="com.joyadata.scc.model.imports.databases.DatasourceInfoAuthorizationOperationDTO">
        select *
        from business_database.datasource_info_authorization_operation
        where product_id = #{productId}
        and item_id = #{projectId}
        and datasource_info_id in
        <foreach collection="datasourceIds" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </select>

    <select id="queryDatasourceReferenceByDatasourceIds"
            resultType="com.joyadata.scc.model.imports.databases.DatasourceReferenceDTO">
        select *
        from business_database.datasource_reference
        where product_id = #{productId}
        and item_id = #{projectId}
        and datasource_info_id in
        <foreach collection="datasourceIds" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </select>

    <select id="queryDatasourceBusinessByUuid"
            resultType="com.joyadata.scc.model.imports.databases.DatasourceBusinessDTO">
        select *
        from business_database.datasource_business
        where uuid in
        <foreach collection="uuids" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </select>
    <select id="queryTaskDefinitionInfoByTaskId"
            resultType="com.joyadata.scc.model.imports.integration.TaskDefinitionInfoDTO">
        select *
        from business_integration.task_definition_info
        where task_id in
        <foreach collection="taskIds" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </select>

    <select id="queryTaskCatalogueByTaskId" resultType="com.joyadata.scc.model.imports.integration.TaskCatalogueDTO">
        select *
        from business_integration.task_catalogue where task_id
        in
        <foreach collection="taskIds" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </select>
    <select id="queryTaskCatalogueByCatalogIds"
            resultType="com.joyadata.scc.model.imports.integration.TaskCatalogueDTO">
        select *
        from business_integration.task_catalogue where catalogue_id in
        <foreach collection="catalogs" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
        GROUP BY catalogue_id
    </select>

    <select id="queryTaskVersionInfoByVersionIds"
            resultType="com.joyadata.scc.model.imports.integration.TaskVersionInfoDTO">
        select *
        from business_integration.task_version_info
        where submit_status=1 and version_id in
        <foreach collection="versionIds" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </select>
    <select id="queryTaskConfigLineInfoByVersionIds"
            resultType="com.joyadata.scc.model.imports.integration.TaskConfigLineInfoDTO">
        select *
        from business_integration.task_config_line_info
        where version_id in
        <foreach collection="versionIds" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </select>
    <select id="queryTaskConfigInfoByVersionIds"
            resultType="com.joyadata.scc.model.imports.integration.TaskConfigInfoDTO">
        select *
        from business_integration.task_config_info
        where version_id in
        <foreach collection="versionIds" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </select>
    <select id="queryDatasourceInfoByName" resultType="com.joyadata.scc.model.imports.databases.DatasourceInfoDTO">
        select *
        from business_database.datasource_info
        where data_name = #{dataName}
          and tenant_id = #{tenantCode} limit 1
    </select>
    <select id="queryDatasourceBusinessByName"
            resultType="com.joyadata.scc.model.imports.databases.DatasourceBusinessDTO">
        select *
        from business_database.datasource_business
        where business_name = #{businessName}
          and tenant_id = #{tenantCode} limit 1
    </select>
    <select id="queryTableExist" resultType="java.lang.String">
        select metadata_table_uuid
        from business_database.metadata_table
        where datasource_info_id = #{datasourceId}
          and table_name = #{tableName}
    </select>
    <select id="queryTaskCatalogueByName" resultType="com.joyadata.scc.model.TempTaskCatalogue">
        select *
        from business_integration.task_catalogue
        where name = #{name}
          and parent_id = #{parentId}
          and tenant_id = #{tenantCode}
          and project_id = #{projectId}
    </select>
    <insert id="insertDatasoruceBusiness">
        insert into
        business_database.datasource_business(business_name,simple_name,depart_name,business_status,business_type,interview_address,online_date,importance_degree,computer_ip,
        create_by,create_time,update_by,update_time,create_user_id,create_dept_id,remark,project_id,tenant_id,uuid,system_level,private_key,public_key,manager_id,manager_name)
        values
        <foreach collection="notExistDatasourceBusinessLists" item="item" separator=",">
            (#{item.businessName},#{item.simpleName},#{item.departName},#{item.businessStatus},#{item.businessType},#{item.interviewAddress},#{item.onlineDate},#{item.importanceDegree},
            #{item.computerIp},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime},#{item.createUserId},#{item.createDeptId},#{item.remark},#{item.projectId},
            #{item.tenantId},#{item.uuid},#{item.systemLevel},#{item.privateKey},#{item.publicKey},#{item.managerId},#{item.managerName})
        </foreach>
    </insert>
    <insert id="insertDatasoruces">
        insert into business_database.datasource_info (datasource_info_id, catalogue_id,data_type,
        data_version, data_name, data_desc, link_json,data_json,status,is_meta,data_type_code,db_name,
        is_deleted,create_by,create_time,update_by,update_time,create_user_id,create_dept_id,remark,project_id,tenant_id,
        first_authorization_time,new_authorization_time,db_version,business_uuid,character_set,ds_time_zone,ip,maximum_number_connections)
        values
        <foreach collection="notExistDatasourceLists" item="item" separator=",">
            (#{item.datasourceInfoId},#{item.catalogueId},#{item.dataType},#{item.dataVersion},#{item.dataName},#{item.dataDesc},
            #{item.linkJson},#{item.dataJson},#{item.status},#{item.isMeta},#{item.dataTypeCode},#{item.dbName},#{item.isDeleted},
            #{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime},#{item.createUserId},#{item.createDeptId},
            #{item.remark},#{item.projectId},#{item.tenantId},#{item.firstAuthorizationTime},#{item.newAuthorizationTime},#{item.dbVersion},
            #{item.businessUuid},#{item.characterSet},#{item.dsTimeZone},#{item.ip},#{item.maximumNumberConnections})
        </foreach>
    </insert>
    <insert id="insertDatasourceInfoAuthorizations">
        insert into business_database.datasource_info_authorization(datasource_info_id, data_type, product_id,
        create_by, create_time, update_by, update_time,
        create_user_id, create_dept_id, remark, project_id,
        tenant_id, authorization_uuid, item_id, type,
        maximum_number_connections)
        values
        <foreach collection="newDatasourceAuthorizationLists" item="item" separator=",">
            (#{item.datasourceInfoId}, #{item.dataType}, #{item.productId}, #{item.createBy}, #{item.createTime},
            #{item.updateBy}, #{item.updateTime}, #{item.createUserId}, #{item.createDeptId}, #{item.remark},
            #{item.projectId}, #{item.tenantId}, #{item.authorizationUuid}, #{item.itemId}, #{item.type},
            #{item.maximumNumberConnections})
        </foreach>
    </insert>
    <insert id="insertDatasourceInfoAuthorizationOperations">
        insert into business_database.datasource_info_authorization_operation(datasource_info_id, data_type, product_id,
        create_by, create_time, update_by,
        update_time, create_user_id,
        create_dept_id, remark, project_id,
        tenant_id, item_id, operation_type)
        values
        <foreach collection="newDatasourceInfoAuthorizationOperationLists" item="item" separator=",">
            (#{item.datasourceInfoId}, #{item.dataType}, #{item.productId}, #{item.createBy}, #{item.createTime},
            #{item.updateBy}, #{item.updateTime}, #{item.createUserId}, #{item.createDeptId}, #{item.remark},
            #{item.projectId}, #{item.tenantId}, #{item.itemId}, #{item.operationType})
        </foreach>
    </insert>
    <insert id="insertDatasourceReferences">
        insert into business_database.datasource_reference(datasource_info_id,data_type,product_id,item_id)
        values
        <foreach collection="newDatasourceReferenceLists" item="item" separator=",">
            (#{item.datasourceInfoId}, #{item.dataType}, #{item.productId}, #{item.itemId})
        </foreach>
    </insert>

    <insert id="insertTaskCatalogues">
        insert into business_integration.task_catalogue
        (catalogue_id,parent_id,parent_level,name,task_id,run_type,task_type,task_status,level,simple_name,options_id,is_leaf,create_by,create_time,update_by,update_time,
        create_user_id,create_dept_id,remark,project_id,tenant_id)
        values
        <foreach collection="notExistTaskCatalogueLists" item="item" separator=",">
            (#{item.catalogueId},#{item.parentId},#{item.parentLevel},#{item.name},#{item.taskId},#{item.runType},#{item.taskType},#{item.taskStatus},#{item.level},#{item.simpleName},
            #{item.optionsId},#{item.isLeaf},#{item.createBy},#{item.updateTime},#{item.updateBy},#{item.updateTime},#{item.createUserId},#{item.createDeptId},#{item.remark},
            #{item.projectId},#{item.tenantId})
        </foreach>
    </insert>
    <insert id="insertTaskConfigInfos">
        insert into business_integration.task_config_info
        (config_id, version_id, plugin_name, plugin_code, plugin_num, connector_type, scene_mode, config_status,
        data_version, data_type, type, plugin_classify_id, plugin_basics_config, plugin_table_structure,
        batch_plugin_table_structures, plugin_advanced_config, batch_plugin_advanced_config, plugin_failed_status,
        is_valid_node_info, create_by, create_time, update_by, update_time, create_user_id, create_dept_id, remark,
        project_id, tenant_id)
        values
        <foreach collection="taskConfigInfoLists" item="item" separator=",">
            (#{item.configId},#{item.versionId},#{item.pluginName},#{item.pluginCode},#{item.pluginNum},#{item.connectorType},#{item.sceneMode},#{item.configStatu},
            #{item.dataVersion},#{item.dataType},#{item.type},#{item.pluginClassifyId},#{item.pluginBasicsConfig},#{item.pluginTableStructure},
            #{item.batchPluginTableStructures},#{item.pluginAdvancedConfig},#{item.batchPluginAdvancedConfig},#{item.pluginFailedStatus},
            #{item.isValidNodeInfo},#{item.createBy},#{item.updateTime},#{item.updateBy},#{item.updateTime},#{item.createUserId},#{item.createDeptId},#{item.remark},
            #{item.projectId},#{item.tenantId}
            )
        </foreach>
    </insert>

    <insert id="insertTaskConfigLineInfos">
        insert into business_integration.task_config_line_info (line_info_id, line_name,
        version_id,input_config_plugin_id, target_config_plugin_id,input_name, output_name,
        input_plugin_classify_id,output_plugin_classify_id, create_by, create_time,update_by,
        update_time, create_user_id, create_dept_id, remark, project_id, tenant_id)
        values
        <foreach collection="taskConfigLineInfoLists" item="item" separator=",">
            (#{item.lineInfoId},#{item.lineName},#{item.versionId},#{item.inputConfigPluginId},#{item.targetConfigPluginId},#{item.inputName},#{item.outputName},
            #{item.inputPluginClassifyId},#{item.outputPluginClassifyId},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime},#{item.createUserId},
            #{item.createDeptId},#{item.remark},#{item.projectId},#{item.tenantId}
            )
        </foreach>
    </insert>
    <insert id="insertTaskDefinitionInfos">
        insert into business_integration.task_definition_info (task_id, task_name, run_type, task_type,
        current_version_id, tmp_version_id, engine_type,source_type, offline_type, online_status, task_status,
        create_by, create_time, update_by, update_time, create_user_id, create_dept_id, remark, project_id,
        tenant_id, task_catalogue_name, auto_layout)
        values
        <foreach collection="taskDefinitionInfoLists" item="item" separator=",">
            (#{item.taskId},#{item.taskName},#{item.runType},#{item.taskType},#{item.currentVersionId},#{item.tmpVersionId},#{item.engineType},
            #{item.sourceType},#{item.offlineType},#{item.onlineStatus},#{item.taskStatus},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime},
            #{item.createUserId},#{item.createDeptId},#{item.remark},#{item.projectId},#{item.tenantId},#{item.taskCatalogueName},#{item.autoLayout})
        </foreach>
    </insert>
    <insert id="insertTaskVersionInfos">
        insert into business_integration.task_version_info
        (version_id,task_id,version_num,environment_configuration,synchronization_strategy,dsg_schedule_configuration,version_status,submit_status,publish_status,
        task_stx_config,process_name,process_type,debug_task_instance_id,task_instance_id,last_run_result,lase_run_time,process_definition_id,create_by,create_time,
        update_by,update_time,create_user_id,create_dept_id,remark,project_id,tenant_id,task_name,task_type,task_remark,task_catalogue_name,submit_time)
        values
        <foreach collection="taskVersionInfoLists" item="item" separator=",">
            (#{item.versionId},#{item.taskId},#{item.versionNum},#{item.environmentConfiguration},#{item.synchronizationStrategy},#{item.dsgScheduleConfiguration},
            #{item.versionStatus},#{item.submitStatus},#{item.publishStatus},#{item.taskStxConfig},#{item.processName},#{item.processType},#{item.debugTaskInstanceId},
            #{item.taskInstanceId},#{item.lastRunResult},#{item.laseRunTime},#{item.processDefinitionId},#{item.createBy},#{item.createTime},#{item.updateBy},
            #{item.updateTime},#{item.createUserId},#{item.createDeptId},#{item.remark},#{item.projectId},#{item.tenantId},#{item.taskName},#{item.taskType},#{item.taskRemark},
            #{item.taskCatalogueName},#{item.submitTime})
        </foreach>
    </insert>

    <select id="queryDatasourceBusinessByBatchNoFlag" resultType="com.joyadata.scc.model.TempDatasourceBusiness">
        select *
        from business_scc.scc_temp_datasource_business
        where batch_no = #{batchNo}
          and exist_flag = #{existFlag}
    </select>
    <select id="queryDatasourceInfoByBatchNoFlag" resultType="com.joyadata.scc.model.TempDatasourceInfo">
        select *
        from business_scc.scc_temp_datasource_info
        where batch_no = #{batchNo}
          and exist_flag = #{existFlag}
    </select>
    <select id="queryDatasourceInfoAuthorizationByBatchNoFlag"
            resultType="com.joyadata.scc.model.TempDatasourceInfoAuthorization">
        select *
        from business_scc.scc_temp_datasource_info_authorization
        where batch_no = #{batchNo}
          and exist_flag = #{existFlag}
    </select>
    <select id="queryDatasourceInfoAuthorizationOperationByBatchNoFlag"
            resultType="com.joyadata.scc.model.TempDatasourceInfoAuthorizationOperation">
        select *
        from business_scc.scc_temp_datasource_info_authorization_operation
        where batch_no = #{batchNo}
          and exist_flag = #{existFlag}
    </select>
    <select id="queryDatasourceReferenceByBatchNoFlag" resultType="com.joyadata.scc.model.TempDatasourceReference">
        select *
        from business_scc.scc_temp_datasource_reference
        where batch_no = #{batchNo}
          and exist_flag = #{existFlag}
    </select>

    <select id="queryTaskCatalogueByBatchNoFlag" resultType="com.joyadata.scc.model.TempTaskCatalogue">
        select *
        from business_scc.scc_temp_task_catalogue
        where batch_no = #{batchNo}
          and exist_flag = #{existFlag}
    </select>
    <select id="queryTaskConfigInfoByBatchNo" resultType="com.joyadata.scc.model.TempTaskConfigInfo">
        select *
        from business_scc.scc_temp_task_config_info
        where batch_no = #{batchNo}
    </select>
    <select id="queryTaskConfigLineInfoByBatchNo"
            resultType="com.joyadata.scc.model.TempTaskConfigLineInfo">
        select *
        from business_scc.scc_temp_task_config_line_info
        where batch_no = #{batchNo}
    </select>
    <select id="queryTaskDefinitionInfoByBatchNo"
            resultType="com.joyadata.scc.model.TempTaskDefinitionInfo">
        select *
        from business_scc.scc_temp_task_definition_info
        where batch_no = #{batchNo}
    </select>
    <select id="queryTaskVersionInfoByBatchNo" resultType="com.joyadata.scc.model.TempTaskVersionInfo">
        select *
        from business_scc.scc_temp_task_version_info
        where batch_no = #{batchNo}
    </select>
    <select id="queryImportInfoByBatchNo" resultType="com.joyadata.scc.model.TempImport">
        select *
        from business_scc.scc_temp_import
        where batch_no = #{batchNo}
    </select>

    <select id="queryDatasourceInfoAuthorizationByDatasourceId"
            resultType="com.joyadata.scc.model.imports.databases.DatasourceInfoAuthorizationDTO">
        select *
        from business_database.datasource_info_authorization
        where datasource_info_id = #{datasourceId}
          and product_id = #{productId}
          and project_id = #{projectId} limit 1
    </select>

    <select id="queryDatasourceInfoAuthorizationOperationByDatasourceId"
            resultType="com.joyadata.scc.model.imports.databases.DatasourceInfoAuthorizationOperationDTO">
        select *
        from business_database.datasource_info_authorization_operation
        where datasource_info_id = #{datasourceId}
          and product_id = #{productId}
          and project_id = #{projectId}
          and operation_type = 'insert' limit 1
    </select>

    <select id="queryDatasourceReferenceByDatasourceId"
            resultType="com.joyadata.scc.model.imports.databases.DatasourceReferenceDTO">
        select *
        from business_database.datasource_reference
        where datasource_info_id = #{datasourceId}
          and product_id = #{productId}
          and item_id = #{projectId}
          and data_type = 'project' limit 1
    </select>

    <!-- 查询指定名称和父目录下的任务目录 -->
    <select id="queryTaskCatalogueByNameAndParent"
            resultType="com.joyadata.scc.model.imports.integration.TaskCatalogueDTO">
        SELECT *
        FROM business_integration.task_catalogue
        WHERE name = #{name}
          AND parent_id = #{parentId}
          AND tenant_id = #{tenantCode} limit 1
    </select>


    <delete id="deleteTaskDefinitionInfoByTaskId">
        delete
        from business_integration.task_definition_info
        where task_id in
        <foreach collection="taskIds" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTaskConfigInfoByVersionIds">
        delete
        from business_integration.task_config_info where version_id in
        <foreach collection="versionIds" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTaskConfigLineInfoByVersionIds">
        delete
        from business_integration.task_config_line_info where version_id in
        <foreach collection="versionIds" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTaskVersionInfoByVersionIds">
        delete from business_integration.task_version_info where version_id in
        <foreach collection="versionIds" item="id" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </delete>

    <select id="queryTaskVersionExist" resultType="com.joyadata.scc.model.imports.integration.TaskVersionInfoDTO">
        select *
        from business_integration.task_version_info
        where version_id = #{versionId}
    </select>
    <select id="getTaskConfigInfoExist" resultType="com.joyadata.scc.model.imports.integration.TaskConfigInfoDTO">
        select *
        from business_integration.task_config_info
        where config_id = #{configId}
    </select>
    <select id="getTaskConfigLineInfoExist"
            resultType="com.joyadata.scc.model.imports.integration.TaskConfigLineInfoDTO">
        select *
        from business_integration.task_config_line_info
        where line_info_id = #{lineInfoId}
    </select>
    <select id="getTaskDefinitionInfoExist"
            resultType="com.joyadata.scc.model.imports.integration.TaskDefinitionInfoDTO">
        select *
        from business_integration.task_definition_info
        where task_id = #{taskId}
    </select>
    <select id="getTaskCatalogExist" resultType="com.joyadata.scc.model.imports.integration.TaskCatalogueDTO">
        SELECT *
        FROM business_integration.task_catalogue
        where catalogue_id = #{catalogueId}
    </select>

    <select id="getDiProcessDefinitionByPorcessDefinitionCode"
            resultType="com.joyadata.scc.model.imports.di.DiProcessDefinitionDTO">
        select *
        from business_integration.di_process_definition
        where id = #{processDefinitionCode}
    </select>
    <select id="getDiProcessTaskRelationByPorcessDefinitionCode"
            resultType="com.joyadata.scc.model.imports.di.DiProcessTaskRelationDTO">
        select *
        from business_integration.di_process_task_relation
        where process_definition_code = #{processDefinitionCode}
    </select>
    <select id="getDiSchedulerByPorcessDefinitionCode"
            resultType="com.joyadata.scc.model.imports.di.DiSchedulerDTO">
        select *
        from business_integration.di_scheduler
        where process_definition_code = #{processDefinitionCode}
    </select>
    <delete id="deleteDiProcessDefinitionByProcessDefinitionId">
        delete
        from business_integration.di_process_definition
        where id = #{processDefinitionCode}
    </delete>
    <delete id="deleteDiProcessTaskRelationByProcessDefinitionId">
        delete
        from business_integration.di_process_task_relation
        where process_definition_code = #{processDefinitionCode}
    </delete>
    <delete id="deleteDiSchedulerByProcessDefinitionId">
        delete
        from business_integration.di_scheduler
        where process_definition_code = #{processDefinitionCode}
    </delete>
    <select id="queryDiProcessDefinitionByBatchNo" resultType="com.joyadata.scc.model.TempDiProcessDefinition">
        select *
        from business_scc.scc_temp_di_process_definition
        where batch_no = #{batchNo}
    </select>
    <select id="queryDiProcessTaskRelationByBatchNo" resultType="com.joyadata.scc.model.TempDiProcessTaskRelation">
        select *
        from business_scc.scc_temp_di_process_task_relation
        where batch_no = #{batchNo}
    </select>
    <select id="queryDiSchedulerByBatchNo" resultType="com.joyadata.scc.model.TempDiScheduler">
        select *
        from business_scc.scc_temp_di_scheduler
        where batch_no = #{batchNo}
    </select>
    <insert id="insertDiProcessDefinitions">
        insert into
        business_integration.di_process_definition(project,id,is_public,project_id,name,version,description,release_state,global_param_map,locations,
        execution_type,is_import,is_test,tenant_code,create_time,last_modification_time,create_by,create_by_name,update_by,del_flag,data_owner_dept_id,
        data_owner_dept_name,data_owner_user_id,data_owner_user_name,pos,readonly,product_id,task_ids,submit_status,publish_status)
        values
        <foreach collection="diProcessDefinitionLists" item="item" separator=",">
            (#{item.project},#{item.id},#{item.isPublic},#{item.projectId},#{item.name},#{item.version},#{item.description},#{item.releaseState},#{item.globalParamMap},#{item.locations},
            #{item.executionType},#{item.isImport},#{item.isTest},#{item.tenantCode},#{item.createTime},#{item.lastModificationTime},#{item.createBy},#{item.createByName},#{item.updateBy},
            #{item.delFlag},#{item.dataOwnerDeptId},#{item.dataOwnerDeptName},#{item.dataOwnerUserId},#{item.dataOwnerUserName},#{item.pos},#{item.readonly},
            #{item.productId},#{item.taskIds},#{item.submitStatus},#{item.publishStatus})
        </foreach>
    </insert>
    <insert id="insertDiProcessTaskRelations">
        insert into business_integration.di_process_task_relation(project, id, is_public, project_id,
        process_definition_code, process_definition_version,
        pre_task_code, pre_task_version, post_task_code,
        post_task_version, condition_type, condition_params,
        is_rely, tenant_code, create_time,
        last_modification_time, create_by, create_by_name,
        update_by, del_flag, data_owner_dept_id,
        data_owner_dept_name, data_owner_user_id,
        data_owner_user_name, pos, readonly)
        values
        <foreach collection="diProcessTaskRelationLists" item="item" separator=",">
            (#{item.project},#{item.id},#{item.isPublic},#{item.projectId},#{item.processDefinitionCode},#{item.processDefinitionVersion},#{item.preTaskCode},#{item.preTaskVersion},
            #{item.postTaskCode},#{item.postTaskVersion},#{item.conditionType},#{item.conditionParams},#{item.isRely},#{item.tenantCode},#{item.createTime},#{item.lastModificationTime},
            #{item.createBy},#{item.createByName},#{item.updateBy},#{item.delFlag},#{item.dataOwnerDeptId},#{item.dataOwnerDeptName},#{item.dataOwnerUserId},
            #{item.dataOwnerUserName},#{item.pos},#{item.readonly})
        </foreach>
    </insert>
    <insert id="insertDiSchedulers">
        insert into
        business_integration.di_scheduler(project,id,is_public,project_id,process_definition_code,calendar_id,start_time,end_time,timezone_id,warning_type,
        warning_group_id,worker_group,crontab,schedule_frequency,failure_strategy,release_state,process_instance_priority,environment_code,stop_strategy,
        tenant_code,create_time,last_modification_time,create_by,create_by_name,update_by,del_flag,data_owner_dept_id,data_owner_dept_name,data_owner_user_id,
        data_owner_user_name,pos,readonly)
        values
        <foreach collection="diSchedulerLists" item="item" separator=",">
            (#{item.project},#{item.id},#{item.isPublic},#{item.projectId},#{item.processDefinitionCode},#{item.calendarId},#{item.startTime},#{item.endTime},#{item.timezoneId}
            ,#{item.warningType},#{item.warningGroupId},#{item.workerGroup},#{item.crontab},#{item.scheduleFrequency},#{item.failureStrategy},#{item.releaseState},
            #{item.processInstancePriority},#{item.environmentCode},#{item.stopStrategy},#{item.tenantCode},#{item.createTime},
            #{item.lastModificationTime},#{item.createBy},#{item.createByName},#{item.updateBy},#{item.delFlag},#{item.dataOwnerDeptId},
            #{item.dataOwnerDeptName},#{item.dataOwnerUserId},#{item.dataOwnerUserName},#{item.pos},#{item.readonly})
        </foreach>
    </insert>
</mapper>