package com.joyadata.scc.controller;

import com.joyadata.annotation.log.Log;
import com.joyadata.cms.model.User;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.imports.integration.IntegrationDTO;
import com.joyadata.scc.service.ImportService;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2025/7/16 18:14
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping("/import")
public class ImportController {

    private String exportPath = "d://aa";
    @Autowired
    private ImportService importService;

    /**
     * 导出任务（json文件）
     */
    @Log("导出任务")
    @Auth
    @GetMapping("/export/exportProcessJson/{processDefinitionCode}")
    public Response exportProcessJson(HttpServletRequest request, @PathVariable String processDefinitionCode) throws IOException {
        String projectId = request.getHeader("projectId");
        String productId = request.getHeader("productId");
        User user = ThreadLocalUserUtil.getUser(User.class);
        IntegrationDTO json = importService.exportProcessJson(productId, projectId, processDefinitionCode, user.getTenantCode(), "001", true);
        return ResponseFactory.makeSuccess(json);
    }

    /**
     * 导入任务（带批次号、覆盖/跳过参数，先写入临时表）
     *
     * @return Response
     */
    @Log("导入任务")
    @Auth
    @PostMapping("/importProcess")
    public Response importProcess(HttpServletRequest request) throws UnsupportedEncodingException {
        String projectId = request.getHeader("projectId");
        // 先存入临时表，返回导入结果
        /*String batchNo = importService.importProcessToTemp(projectId);
        return ResponseFactory.makeSuccess(batchNo);*/
        return null;
    }

    @Log("数据导入同步")
    @Auth
    @PostMapping("/sync/{batchNo}")
    public Response sync(@PathVariable String batchNo, HttpServletRequest request) {
        String projectId = request.getHeader("projectId");
        String productId = request.getHeader("productId");
        User user = ThreadLocalUserUtil.getUser(User.class);
        importService.sync(batchNo, projectId, productId);
        return ResponseFactory.makeSuccess(batchNo);
    }

    @PostMapping("/testPost/{time}")
    public Map<String, String> testPost(@PathVariable long time) throws InterruptedException {
        Map<String, String> map = new HashMap<>();
        Thread.sleep(time);
        map.put("datalist", "00001");
        map.put("user", "张三");
        return map;
    }
}
