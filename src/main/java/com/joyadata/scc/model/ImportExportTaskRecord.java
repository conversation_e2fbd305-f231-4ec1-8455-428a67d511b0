package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/8/8 13:42
 */
@Data
@JoyadataTable(name = "scc_import_export_task_record", label = "scc_import_export_task_record", comment = "一体化导入导出记录")
public class ImportExportTaskRecord extends BaseBean {
    @JoyadataColumn(label = "批次号")
    private String batchNo;
    @JoyadataColumn(label = "记录名称")
    private String name;
    @JoyadataColumn(label = "类型，0导入，1导出")
    private Integer type;
    @JoyadataColumn(label = "状态，0失败，1成功，2运行中")
    private Integer state;
    @JoyadataColumn(label = "工作流数量")
    private Integer workflowCount;
    @JoyadataColumn(label = "任务数量")
    private Integer taskCount;
    @JoyadataColumn(label = "数据源数量")
    private Integer datasourceCount;
    @JoyadataColumn(label = "导出工作流定义code，多个逗号隔开")
    private String processDefinitionCodes;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "产品id")
    private String productId;
    @JoyadataColumn(label = "生成文件路径")
    private String filePath;
    @JoyadataColumn(label = "执行机器IP")
    private String ip;
    @JoyadataColumn(label = "导入前json")
    private String beforeJson;
}
