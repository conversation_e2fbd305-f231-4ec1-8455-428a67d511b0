package com.joyadata.scc.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.joyadata.cms.model.User;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.model.sql.InCondition;
import com.joyadata.scc.dto.ProcessExportDTO;
import com.joyadata.scc.internal.TaskHandler;
import com.joyadata.scc.internal.dialect.SqlTaskHandler;
import com.joyadata.scc.internal.dialect.StxTaskHandler;
import com.joyadata.scc.mapper.ExportTaskMapper;
import com.joyadata.scc.model.ImportExportTaskRecord;
import com.joyadata.scc.model.ImportExportTaskRecordDetail;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.ProcessTaskRelation;
import com.joyadata.scc.model.Task;
import com.joyadata.scc.model.TempDatasourceBusiness;
import com.joyadata.scc.model.TempDatasourceInfo;
import com.joyadata.scc.model.TempDatasourceInfoAuthorization;
import com.joyadata.scc.model.TempDatasourceInfoAuthorizationOperation;
import com.joyadata.scc.model.TempDatasourceReference;
import com.joyadata.scc.model.TempDiProcessDefinition;
import com.joyadata.scc.model.TempDiProcessTaskRelation;
import com.joyadata.scc.model.TempDiScheduler;
import com.joyadata.scc.model.TempImport;
import com.joyadata.scc.model.TempTaskCatalogue;
import com.joyadata.scc.model.TempTaskConfigInfo;
import com.joyadata.scc.model.TempTaskConfigLineInfo;
import com.joyadata.scc.model.TempTaskDefinitionInfo;
import com.joyadata.scc.model.TempTaskVersionInfo;
import com.joyadata.scc.model.imports.databases.DatasourceBusinessDTO;
import com.joyadata.scc.model.imports.databases.DatasourceInfoAuthorizationDTO;
import com.joyadata.scc.model.imports.databases.DatasourceInfoAuthorizationOperationDTO;
import com.joyadata.scc.model.imports.databases.DatasourceInfoDTO;
import com.joyadata.scc.model.imports.databases.DatasourceReferenceDTO;
import com.joyadata.scc.model.imports.di.DiProcessDefinitionDTO;
import com.joyadata.scc.model.imports.di.DiProcessTaskRelationDTO;
import com.joyadata.scc.model.imports.di.DiSchedulerDTO;
import com.joyadata.scc.model.imports.integration.IntegrationDTO;
import com.joyadata.scc.model.imports.integration.TaskCatalogueDTO;
import com.joyadata.scc.model.imports.integration.TaskConfigInfoDTO;
import com.joyadata.scc.model.imports.integration.TaskConfigLineInfoDTO;
import com.joyadata.scc.model.imports.integration.TaskDefinitionInfoDTO;
import com.joyadata.scc.model.imports.integration.TaskVersionInfoDTO;
import com.joyadata.scc.model.imports.integration.change.TablesChangesDTO;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.util.GeneratorUtil;
import com.joyadata.util.ThreadLocalUserUtil;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import groovy.lang.Tuple4;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.joyadata.scc.model.Task.TaskType.SQL;
import static com.joyadata.scc.model.Task.TaskType.STX;

/**
 * <AUTHOR>
 * @Date 2025/7/16 18:48
 */
@Slf4j
@Service
public class ImportService {
    @Autowired
    private ExportTaskMapper exportTaskMapper;
    @Autowired
    private TaskService taskService;
    @Autowired
    private TempDatasourceBusinessService datasourceBusinessService;
    @Autowired
    private TempDatasourceInfoService datasourceInfoService;
    @Autowired
    private TempDatasourceInfoAuthorizationService datasourceInfoAuthorizationService;
    @Autowired
    private TempDatasourceInfoAuthorizationOperationService datasourceInfoAuthorizationOperationService;
    @Autowired
    private TempDatasourceReferenceService datasourceReferenceService;

    @Autowired
    private TempTaskCatalogueService taskCatalogueService;
    @Autowired
    private TempTaskConfigInfoService taskConfigInfoService;
    @Autowired
    private TempTaskConfigLineInfoService taskConfigLineInfoService;
    @Autowired
    private TempTaskDefinitionInfoService taskDefinitionInfoService;
    @Autowired
    private TempTaskVersionInfoService taskVersionInfoService;

    @Value("${dolphinscheduler}")
    private String dolphinscheduler;
    @Autowired
    private HttpRequestFeignService httpRequestFeignService;

    @Autowired
    private TempImportService tempImportService;

    @Autowired
    private ProcessDefinitionService processDefinitionService;

    @Autowired
    private ProcessDefinitionCatalogService processDefinitionCatalogService;

    @Autowired
    ImportExportTaskRecordService importExportTaskRecordService;
    @Autowired
    ProcessTaskRelationService taskRelationService;

    @Autowired
    private ImportExportTaskRecordDetailService importExportTaskRecordDetailService;

    @Autowired
    private TempDiProcessDefinitionService tempDiProcessDefinitionService;
    @Autowired
    private TempDiProcessTaskRelationService tempDiProcessTaskRelationService;
    @Autowired
    private TempDiSchedulerService tempDiSchedulerService;

    //导入加后缀名称，目前不加。
    private static final String IMPORT_NAME = "";

    private static final Map<String, TaskHandler> handlerMap = new HashMap<>();
    @Autowired
    private StringRedisTemplate redisTemplate;
    private String IMPORT_KEY = "dedp:scc:import:";


    static {
        handlerMap.put(SQL, new SqlTaskHandler());
        handlerMap.put(STX, new StxTaskHandler());
    }

    /**
     * 导出任务为json文件，写到指定路径
     */
    public IntegrationDTO exportProcessJson(String productId, String projectId, String processDefinitionCode, String tenantCode, String batchNo, boolean detailFlag) throws IOException {
        List<ProcessExportDTO> processExportDTO = new ArrayList<>();

        IntegrationDTO integrationDTO = new IntegrationDTO();
        //工作流
        List<Task> tasks = taskService.getQuery().eq("processDefinitionCode", processDefinitionCode).list();
        //根据项目定义code查询到数据源id
        List<String> datasourceIds = getDatasourcesIds(tasks);
        log.info("工作流定义code {} 查询到的数据源数量为 {},分别为 {}", processDefinitionCode, datasourceIds.size(), datasourceIds);
        if (datasourceIds.size() > 0) {
            //导出数据源信息
            List<DatasourceInfoDTO> datasourceInfoDTO = exportTaskMapper.queryDatasourceInfoById(datasourceIds);
            integrationDTO.setDatasource(datasourceInfoDTO);
            if (null != datasourceInfoDTO && datasourceInfoDTO.size() > 0) {
                List<String> uuids = datasourceInfoDTO.stream().map(t -> t.getBusinessUuid()).collect(Collectors.toList());
                List<DatasourceBusinessDTO> datasourceBusinessDTOS = exportTaskMapper.queryDatasourceBusinessByUuid(uuids);
                log.info("工作流定义code {} 查询到的数据源所属系统数量 {}", processDefinitionCode, datasourceBusinessDTOS.size());
                integrationDTO.setDatasourceBusiness(datasourceBusinessDTOS);
            }
            List<DatasourceInfoAuthorizationDTO> datasourceInfoAuthorizations = exportTaskMapper.queryDatasourceInfoAuthorizationByDatasourceIds(productId, projectId, datasourceIds);
            log.info("工作流定义code {} 获取到数据源授权信息 {} 条", processDefinitionCode, datasourceInfoAuthorizations.size());
            integrationDTO.setDatasourceInfoAuthorizations(datasourceInfoAuthorizations);
        }


        //List<DatasourceInfoAuthorizationOperationDTO> datasourceInfoAuthorizationOperations = exportTaskMapper.queryDatasourceInfoAuthorizationOperationByDatasourceIds(productId, projectId, datasourceIds);
        //log.info("工作流定义code {} 获取到数据源授权操作信息 {} 条", processDefinitionCode, datasourceInfoAuthorizationOperations.size());
        //integrationDTO.setDatasourceInfoAuthorizationOperations(datasourceInfoAuthorizationOperations);
        //List<DatasourceReferenceDTO> datasourceReferences = exportTaskMapper.queryDatasourceReferenceByDatasourceIds(productId, projectId, datasourceIds);
        //log.info("工作流定义code {} 获取到数据源引入信息 {} 条", processDefinitionCode, datasourceReferences.size());
        //integrationDTO.setDatasourceReferences(datasourceReferences);
        List<String> taskIds = tasks.stream().map(Task::getId).collect(Collectors.toList());
        //处理调度任务，调度任务的id 对应的是工作流的id
        taskIds.add(processDefinitionCode);
        log.info("工作流定义code {},查询到的任务数量 {},任务code分别为 {}", processDefinitionCode, tasks.size(), taskIds);
        //导出集成信息
        List<TaskDefinitionInfoDTO> taskDefinitionInfoDTOS = exportTaskMapper.queryTaskDefinitionInfoByTaskId(taskIds);
        log.info("工作流定义code {} 查询到集成任务数量 {}", processDefinitionCode, taskDefinitionInfoDTOS.size());
        integrationDTO.setTaskDefinitionInfos(taskDefinitionInfoDTOS);
        List<TaskCatalogueDTO> taskCatalogueDTOS = exportTaskMapper.queryTaskCatalogueByTaskId(taskIds);
        integrationDTO.setTaskCatalogues(taskCatalogueDTOS);
        //taskId 作为key，valye是taskId，CatalogueId，ParentId，ParentLevel
        Map<String, Tuple4<String, String, String, String>> catalogInfos = taskCatalogueDTOS.stream().map(t -> new Tuple4<>(t.getTaskId(), t.getCatalogueId(), t.getParentId(), t.getParentLevel()))
                .collect(Collectors.toMap(Tuple4::getV1, t -> t));
        List<String> catalogs = catalogInfos.values().stream()
                .map(t -> t.getV4())
                .filter(v4 -> v4 != null && !v4.isEmpty())
                .flatMap(v4 -> Arrays.stream(v4.split(",")))
                .collect(Collectors.toList());
        catalogs = catalogs.stream().distinct().collect(Collectors.toList());
        List<TaskCatalogueDTO> taskCatalogues = new ArrayList<>();
        if (null != catalogs && catalogs.size() > 0) {
            taskCatalogues = exportTaskMapper.queryTaskCatalogueByCatalogIds(catalogs);
        }
        integrationDTO.setTaskCatalogueInfos(taskCatalogues);
        List<String> versionIds = taskDefinitionInfoDTOS.stream().map(TaskDefinitionInfoDTO::getCurrentVersionId).collect(Collectors.toList());
        //版本信息
        List<TaskVersionInfoDTO> taskVersionInfoDTOS = new ArrayList<>();
        List<TaskConfigLineInfoDTO> taskConfigLineInfoDTOS = new ArrayList<>();
        List<TaskConfigInfoDTO> taskConfigInfos = new ArrayList<>();
        if (null != versionIds && versionIds.size() > 0) {
            taskVersionInfoDTOS = exportTaskMapper.queryTaskVersionInfoByVersionIds(versionIds);
            //连线信息
            taskConfigLineInfoDTOS = exportTaskMapper.queryTaskConfigLineInfoByVersionIds(versionIds);
            taskConfigInfos = exportTaskMapper.queryTaskConfigInfoByVersionIds(versionIds);
        }

        integrationDTO.setTaskVersionInfos(taskVersionInfoDTOS);
        integrationDTO.setTaskConfigLineInfos(taskConfigLineInfoDTOS);
        integrationDTO.setTaskConfigInfos(taskConfigInfos);
        List<DiProcessDefinitionDTO> diProcessDefinition = exportTaskMapper.getDiProcessDefinitionByPorcessDefinitionCode(processDefinitionCode);
        integrationDTO.setDiProcessDefinition(diProcessDefinition);
        List<DiProcessTaskRelationDTO> diProcessTaskRelations = exportTaskMapper.getDiProcessTaskRelationByPorcessDefinitionCode(processDefinitionCode);
        integrationDTO.setDiProcessTaskRelations(diProcessTaskRelations);
        List<DiSchedulerDTO> diScheduler = exportTaskMapper.getDiSchedulerByPorcessDefinitionCode(processDefinitionCode);
        integrationDTO.setDiScheduler(diScheduler);
        //导出调度信息
        String url = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", projectId) + "/batch_export_json?codes=" + processDefinitionCode;
        Map<String, Object> headers = Utils.getHeader();
        Map result = httpRequestFeignService.get4url(url, headers, Map.class);
        if (Integer.parseInt(String.valueOf(result.get("code"))) == 0) {
            Object data = result.get("data");
            processExportDTO = JSON.parseArray(data.toString(), ProcessExportDTO.class);
        }
        List<ProcessExportDTO> processExportDTOList = processDefinitionService.initProcessDefinitionCatalog(processExportDTO);
        List<ProcessExportDTO> sccInfos = processDefinitionService.initTaskCatalog(processExportDTOList);
        if (null == sccInfos || sccInfos.size() == 0) {
            Map<String, List<String>> deleteInfos = getDeleteSccInfos(processDefinitionCode);
            integrationDTO.setDeleteProcessInfos(deleteInfos);
        } else {
            integrationDTO.setSccInfos(sccInfos);
        }
        if (detailFlag) {
            saveTaskRecordDetail(batchNo, integrationDTO, projectId, tenantCode);
        }
        return integrationDTO;
    }

    private Map<String, List<String>> getDeleteSccInfos(String processDefinitionCode) {
        Map<String, List<String>> deleteInfos = new HashMap<>();
        deleteInfos.put("processCode", Arrays.asList(processDefinitionCode));
        List<String> taskIds = taskRelationService.getQuery().eq("processDefinitionCode", processDefinitionCode).filters("postTaskCode").listValue("postTaskCode", String.class);
        deleteInfos.put("taskCode", taskIds);
        return deleteInfos;
    }

    private List<ImportExportTaskRecordDetail> getsaveTaskRecordDetails(String batchNo, IntegrationDTO integrationDTO, String projectId, String tenantCode) {
        List<DatasourceInfoDTO> datasourceInfos = integrationDTO.getDatasource();
        List<ImportExportTaskRecordDetail> details = new ArrayList<>();
        datasourceInfos.forEach(datasourceInfo -> {
            ImportExportTaskRecordDetail detail = new ImportExportTaskRecordDetail(batchNo, 0, datasourceInfo.getDataName(), 1, "导出", projectId);
            //detail.setTenantCode(tenantCode);
            details.add(detail);
        });

        List<ProcessExportDTO> sccInfos = integrationDTO.getSccInfos();
        sccInfos.stream().forEach(sccInfo -> {
            ProcessDefinition pd = sccInfo.getProcessDefinition();
            ImportExportTaskRecordDetail detail = new ImportExportTaskRecordDetail(batchNo, 2, pd.getName(), 1, "导出", projectId);
            //detail.setTenantCode(tenantCode);
            details.add(detail);
            List<Task> tasks = sccInfo.getTaskDefinitionList();
            tasks.stream().forEach(task -> {
                ImportExportTaskRecordDetail detail1 = new ImportExportTaskRecordDetail(batchNo, 1, task.getName(), 1, "导出", projectId);
                //detail1.setTenantCode(tenantCode);
                details.add(detail1);
            });
        });
        return details;
    }

    private void saveTaskRecordDetail(String batchNo, IntegrationDTO integrationDTO, String projectId, String tenantCode) {
        List<ImportExportTaskRecordDetail> details = getsaveTaskRecordDetails(batchNo, integrationDTO, projectId, tenantCode);
        importExportTaskRecordDetailService.add(details);
    }

    private List<String> getDatasourcesIds(List<Task> tasks) {
        List<String> datasourceIds = new ArrayList<>();
        tasks.forEach(task -> {
            TaskHandler handler = handlerMap.get(task.getTaskType());
            if (null != handler) {
                List<String> ids = handler.extractDatasourceIds(task.getTaskParams());
                datasourceIds.addAll(ids);
            }
        });
        return datasourceIds;
    }

    /**
     * 导入任务，先写入临时表
     *
     * @param jsonFilePath JSON文件路径
     * @param projectId    项目ID
     * @param batchNo      批次号
     * @return 导入结果
     */
    @Transactional
    public void importProcessToTemp(String jsonFilePath, String projectId, String productId, String sccProductId, String batchNo) {
        log.info("[BatchNo:{}] 开始处理JSON文件导入到临时表：{}", batchNo, jsonFilePath);

        String importJson = "";
        File file = new File(jsonFilePath);
        try (InputStream inputStream = new FileInputStream(file)) {
            importJson = IOUtils.toString(inputStream, "UTF-8");
            log.info("[BatchNo:{}] 成功读取JSON文件，大小：{} 字符", batchNo, importJson.length());
        } catch (IOException e) {
            log.error("[BatchNo:{}] 读取JSON文件失败：{}", batchNo, e.getMessage());
            throw new AppErrorException("读取JSON文件失败，原因：{}", e.getMessage());
        }
        try {
            IntegrationDTO integrationDTO = JSONObject.parseObject(importJson, IntegrationDTO.class);
            User user = ThreadLocalUserUtil.getUser(User.class);
            //debug信息 这里的替换id是catalog的信息  是根据id 要清理的id
            Map<String, String> needChangeIds = new HashMap<>();
            //List<String> existTaskIds = checkTaskAlreadyExist(integrationDTO.getTaskCatalogueInfos(), user.getTenantCode(),projectId, needChangeIds);
            log.info("[BatchNo:{}] 开始处理导入工作，用户：{}", batchNo, user.getUsername());
            log.info("先处理目录信息");
            newTask(integrationDTO, user, projectId, productId, sccProductId, batchNo, needChangeIds);

        } catch (Exception e) {
            log.error("[BatchNo:{}] JSON文件导入到临时表失败：{}", batchNo, e.getMessage(), e);
            throw new AppErrorException("JSON文件导入到临时表失败，原因：{}", e.getMessage());
        }
    }

    @Transactional
    public void newTask(IntegrationDTO integrationDTO, User user, String projectId, String productId, String sccProductId, String batchNo, Map<String, String> needChangeIds) {
        List<ImportExportTaskRecordDetail> saveTaskRecordDetails = new ArrayList<>();
        //处理导入工作
        //导入数据源所属系统
        log.info("[BatchNo:{}] 开始处理数据源所属系统，数量：{}", batchNo,
                integrationDTO.getDatasourceBusiness() != null ? integrationDTO.getDatasourceBusiness().size() : 0);
        Tuple2<List<DatasourceBusinessDTO>, Map<String, String>> checkDatasourceBusinessResult =
                checkDatasourceBusinessExist(integrationDTO.getDatasourceBusiness(), user.getTenantCode());
        //checkDatasourceBusinessResult.getV2  里面存放map，
        // 如果数据源有存在的，则key是导入的旧ID，value是目标数据源存在的id，
        // 如果查询信息在目标端不存在，则使用原来的id
        Map<String, String> needChangeInfos = checkDatasourceBusinessResult.getV2();
        List<TempDatasourceBusiness> saveDatasourceBusinessList = convertDatasourceBusiness(checkDatasourceBusinessResult.getV1(), batchNo, user, projectId);
        datasourceBusinessService.add(saveDatasourceBusinessList);
        log.info("[BatchNo:{}] 数据源所属系统处理完成，新增数量：{}", batchNo, saveDatasourceBusinessList.size());
        //导入数据源
        //要判断数据源是否存在，如果存在则跳过，将导入的数据源id，更新为存在的数据源id，如果不存在，则导入到临时表
        log.info("[BatchNo:{}] 开始处理数据源，数量：{}", batchNo,
                integrationDTO.getDatasource() != null ? integrationDTO.getDatasource().size() : 0);
        Tuple2<List<DatasourceInfoDTO>, Map<String, String>> checkDatasourceResult = checkDatasourceExist(integrationDTO.getDatasource(), user.getTenantCode(), needChangeInfos,
                saveTaskRecordDetails, batchNo, projectId);
        //添加不存在的数据源
        List<TempDatasourceInfo> saveDatasourceList = convertDatasource(checkDatasourceResult.getV1(), batchNo, user, projectId, needChangeInfos);
        datasourceInfoService.add(saveDatasourceList);
        log.info("[BatchNo:{}] 数据源处理完成，保存数量：{}", batchNo, saveDatasourceList.size());

        log.info("开始处理数据源授权信息");
        List<String> datasourceIds = getNewDatasourceIds(needChangeInfos, integrationDTO.getDatasource().stream().map(DatasourceInfoDTO::getDatasourceInfoId).filter(Objects::nonNull).collect(Collectors.toList()));
        List<DatasourceInfoAuthorizationDTO> checkDatasourceAuthorizationResult = checkDatasourceAuthorizationExist(datasourceIds, projectId, productId);
        List<TempDatasourceInfoAuthorization> tempDatasourceInfoAuthorizations = convertDatasourceAuthorization(checkDatasourceAuthorizationResult, batchNo, user);
        datasourceInfoAuthorizationService.add(tempDatasourceInfoAuthorizations);
        log.info("[BatchNo:{}] 数据源授权处理完成，保存数量：{}", batchNo, tempDatasourceInfoAuthorizations.size());

        log.info("开始处理数据源操作信息");
        List<DatasourceInfoAuthorizationOperationDTO> checkOperationResult = checkDatasourceAuthorizationOperationExist(datasourceIds, projectId, productId);
        List<TempDatasourceInfoAuthorizationOperation> tempOperations = convertDatasourceAuthorizationOperation(checkOperationResult, batchNo, user);
        datasourceInfoAuthorizationOperationService.add(tempOperations);

        log.info("开始处理引入信息");
        List<DatasourceReferenceDTO> checkDatasourceReferenceResult = checkDatasourceReferenceExist(datasourceIds, projectId, productId);
        List<TempDatasourceReference> tempReferences = convertDatasourceReference(checkDatasourceReferenceResult, batchNo, user);
        datasourceReferenceService.add(tempReferences);

        log.info("开始处理集成数据，先查询任务是否存在");

        //查询处理需要删除工作流信息
        log.info("查询是否存在工作流");
        Tuple2<Map<String, String>, Map<String, String>> deleteTaskIds = getProcessDefinitionNeedDelete(integrationDTO.getSccInfos(), projectId);
        log.info("开始查询数据，如果有相同的则替换，没有则保持原样 {}", deleteTaskIds);
        log.info("开始备份历史数据");
        backupHistoryData(deleteTaskIds.getV1(), sccProductId, projectId, batchNo, user.getTenantCode());
        log.info("备份历史数据完成");
        //替换 pluginBasicsConfig 考虑 pluginClassifyId是1 是source、2是sink 抽取源端、目标端 tableId
        //key是旧的表id，value是新的表id
        //替换数据源id、表id
        Map<String, String> changeTableIds = getTableIdNeedChange(integrationDTO.getTaskConfigInfos(), checkDatasourceResult.getV2());
        //处理version_id替换
        getTaskVersionNeedChange(changeTableIds, integrationDTO.getTaskVersionInfos());
        //处理task_config_info的config_id和 scc_temp_task_config_line_info里面的id
        getTaskConfigInfoNeedChange(changeTableIds, integrationDTO.getTaskConfigInfos());

        getTaskConfigInfoLineNeedChange(changeTableIds, integrationDTO.getTaskConfigLineInfos());
        //处理taskDefinitionInfo的task_id
        getTaskDefinitionInfoNeedChange(changeTableIds, integrationDTO.getTaskDefinitionInfos());
        //处理目录信息
        //getTaskCatalogueNeedAddId(changeTableIds, integrationDTO.getTaskCatalogueInfos(), user.getTenantCode(), projectId);
        //处理工作流和任务的id信息
        getProcessNeedChange(changeTableIds, integrationDTO.getSccInfos(), deleteTaskIds, saveTaskRecordDetails, batchNo, projectId);

        importExportTaskRecordDetailService.add(saveTaskRecordDetails);
        log.info("替换参数信息是 {}", changeTableIds);
        //需要替换目录id
        List<TaskCatalogueDTO> taskCatalogueChangeInfoList = getTaskCatalogueNeedChanage(integrationDTO.getTaskCatalogueInfos(), user, projectId, changeTableIds);
        List<TempTaskCatalogue> taskCatalogueInfoList = convertIntegrationTaskCatalogue(taskCatalogueChangeInfoList, batchNo, user, projectId, checkDatasourceResult.getV2(), changeTableIds);
        taskCatalogueService.add(taskCatalogueInfoList);

        //通过taskid 查找task_definition_info 获取到对应的版本信息

        List<TempTaskConfigInfo> taskConfigInfoList = convertIntegrationTaskConfigInfo(integrationDTO.getTaskConfigInfos(), batchNo, user, projectId, checkDatasourceResult.getV2(), changeTableIds);
        taskConfigInfoService.add(taskConfigInfoList);

        List<TempTaskConfigLineInfo> taskConfigLineInfoList = convertIntegrationTaskConfigLineInfo(integrationDTO.getTaskConfigLineInfos(), batchNo, user, projectId, checkDatasourceResult.getV2(), changeTableIds);
        taskConfigLineInfoService.add(taskConfigLineInfoList);

        List<TempTaskDefinitionInfo> taskDefinitionInfoList = convertIntegrationTaskDefinitionInfo(integrationDTO.getTaskDefinitionInfos(), batchNo, user, projectId, checkDatasourceResult.getV2(), changeTableIds);
        taskDefinitionInfoService.add(taskDefinitionInfoList);

        // 要替换taskStxConfig
        List<TempTaskVersionInfo> taskVersionInfoList = convertIntegrationTaskVersionInfo(integrationDTO.getTaskVersionInfos(), batchNo, user, projectId, checkDatasourceResult.getV2(), changeTableIds);
        taskVersionInfoService.add(taskVersionInfoList);
        log.info("开始处理di信息");
        List<TempDiProcessDefinition> diProcessDefinitionList = convertIntegrationDiProcessDefinition(integrationDTO.getDiProcessDefinition(), batchNo, user, projectId, checkDatasourceResult.getV2(), changeTableIds);
        if (diProcessDefinitionList.size() > 0) {
            tempDiProcessDefinitionService.add(diProcessDefinitionList);
        }
        List<TempDiProcessTaskRelation> diProcessTaskRelations = convertIntegrationDiProcessTaskRelation(integrationDTO.getDiProcessTaskRelations(), batchNo, user, projectId, checkDatasourceResult.getV2(), changeTableIds);
        if (diProcessTaskRelations.size() > 0) {
            tempDiProcessTaskRelationService.add(diProcessTaskRelations);
        }
        List<TempDiScheduler> diSchedulers = convertIntegrationDiScheduler(integrationDTO.getDiScheduler(), batchNo, user, projectId, checkDatasourceResult.getV2(), changeTableIds);
        if (diSchedulers.size() > 0) {
            tempDiSchedulerService.add(diSchedulers);
        }
        // 处理调度信息
        log.info("[BatchNo:{}] 开始处理调度信息，数量：{}", batchNo,
                integrationDTO.getSccInfos() != null ? integrationDTO.getSccInfos().size() : 0);
        TempImport tempImportList = convertSccTempImport(integrationDTO.getSccInfos(), batchNo, user, projectId,
                checkDatasourceResult.getV2(), changeTableIds);
        tempImportService.add(tempImportList);
        log.info("[BatchNo:{}] 调度信息处理完成", batchNo);

        log.info("[BatchNo:{}] JSON文件导入到临时表完成", batchNo);
        sync(batchNo, projectId, productId);
    }

    private void backupHistoryData(Map<String, String> v1, String sccProductId, String projectId, String batchNo, String tenantCode) {
        List<IntegrationDTO> list = new ArrayList<>();
        v1.values().stream().forEach(processDefinId -> {
            try {

                list.add(exportProcessJson(sccProductId, projectId, processDefinId, tenantCode, batchNo, false));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
        ImportExportTaskRecord record = new ImportExportTaskRecord();
        record.setBeforeJson(JSONObject.toJSONString(list));
        importExportTaskRecordService.updateBy(new EqCondition("batchNo", batchNo), record);
    }

    /**
     * @param sccInfos
     * @param productId
     * @param projectId
     * @return v1 是工作流信息，v2是任务信息
     */
    private Tuple2<Map<String, String>, Map<String, String>> getProcessDefinitionNeedDelete(List<ProcessExportDTO> sccInfos, String projectId) {
        Map<String, String> processDefinitionMaps = new HashMap<>();
        Map<String, String> taskIdMaps = new HashMap<>();
        sccInfos.forEach(sccInfo -> {
            ProcessDefinition processDefinition = sccInfo.getProcessDefinition();
            if (null != processDefinition.getIsImport()) {
                String catalogId = null;
                String productId = processDefinition.getProductId();
                if (processDefinition.getIsImport()) {
                    Tuple2<String, String> catalogTuple = processDefinitionCatalogService.initDefaultCatalogId(productId, projectId, processDefinition.getCatalogNames());
                    catalogId = catalogTuple.getV1();
                } else {
                    Tuple2<String, String> catalogTuple = processDefinitionCatalogService.initCustomCatalogId(productId, projectId, processDefinition.getCatalogNames());
                    catalogId = catalogTuple.getV1();
                }
                String name = processDefinition.getName();
                String processDefinitionId = processDefinitionService.getQuery().eq("catalogId", catalogId).eq("name", name).oneValue("id", String.class);
                if (StringUtils.isNotEmpty(processDefinitionId)) {
                    List<ProcessTaskRelation> taskIdLists = taskRelationService.getQuery().eq("processDefinitionCode", processDefinitionId).withs("taskName")
                            .filters("postTaskCode", "taskName").list();
                    processDefinitionMaps.put(name, processDefinitionId);
                    taskIdLists.stream().forEach(taskIdList -> taskIdMaps.put(taskIdList.getTaskName(), taskIdList.getPostTaskCode()));
                }
            }
        });
        return new Tuple2<>(processDefinitionMaps, taskIdMaps);
    }

    /**
     * @param needChangeInfos  替换后的map
     * @param oldDatasourceIds 旧数据源的id
     * @return
     */
    private List<String> getNewDatasourceIds(Map<String, String> needChangeInfos, List<String> oldDatasourceIds) {
        List<String> newDatasourceIds = new ArrayList<>();
        oldDatasourceIds.stream().forEach(oldDatasourceId -> {
            if (needChangeInfos.containsKey(oldDatasourceIds)) {
                newDatasourceIds.add(needChangeInfos.get(oldDatasourceId));
            } else {
                newDatasourceIds.add(oldDatasourceId);
            }
        });
        return newDatasourceIds;
    }

    private String getBatchNo() {
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String key = IMPORT_KEY + dateStr;
        Long serialNumber = redisTemplate.opsForValue().increment(key);
        if (serialNumber != null && serialNumber == 1L) {
            redisTemplate.expire(key, 25, TimeUnit.HOURS);
        }
        return dateStr + String.format("%06d", serialNumber);
    }

    /**
     * 查询版本，如果存在，则使用原来的id，如果不存在，则新增一个
     *
     * @param changeTableIds
     * @param taskVersionInfos
     */
    private void getTaskVersionNeedChange(Map<String, String> changeTableIds, List<TaskVersionInfoDTO> taskVersionInfos) {
        taskVersionInfos.stream().forEach(taskVersionInfo -> {
            TaskVersionInfoDTO exist = exportTaskMapper.queryTaskVersionExist(taskVersionInfo.getVersionId());
            if (null != exist) {
                changeTableIds.put(taskVersionInfo.getVersionId(), Utils.getUUID());
            } else {
                changeTableIds.put(taskVersionInfo.getVersionId(), Utils.getUUID());
            }
        });
    }

    private void getTaskConfigInfoNeedChange(Map<String, String> changeTableIds, List<TaskConfigInfoDTO> taskConfigInfos) {
        taskConfigInfos.stream().forEach(taskConfigInfo -> {
            TaskConfigInfoDTO exist = exportTaskMapper.getTaskConfigInfoExist(taskConfigInfo.getConfigId());
            if (null != exist) {
                //这里无法使用原来的，会报错
                changeTableIds.put(taskConfigInfo.getConfigId(), Utils.getUUID());
            } else {
                changeTableIds.put(taskConfigInfo.getConfigId(), Utils.getUUID());
            }
        });
    }

    private void getTaskConfigInfoLineNeedChange(Map<String, String> changeTableIds, List<TaskConfigLineInfoDTO> lines) {
        lines.stream().forEach(line -> {
            TaskConfigLineInfoDTO exist = exportTaskMapper.getTaskConfigLineInfoExist(line.getLineInfoId());
            if (null != exist) {
                changeTableIds.put(line.getLineInfoId(), Utils.getUUID());
            } else {
                changeTableIds.put(line.getLineInfoId(), Utils.getUUID());
            }
        });
    }

    private void getTaskDefinitionInfoNeedChange(Map<String, String> changeTableIds, List<TaskDefinitionInfoDTO> taskDefinitionInfos) {
        taskDefinitionInfos.stream().forEach(taskDefinitionInfo -> {
            TaskDefinitionInfoDTO task = exportTaskMapper.getTaskDefinitionInfoExist(taskDefinitionInfo.getTaskId());
            if (null != task) {
                changeTableIds.put(taskDefinitionInfo.getTaskId(), GeneratorUtil.genCode());
            } else {
                changeTableIds.put(taskDefinitionInfo.getTaskId(), GeneratorUtil.genCode());
            }
        });
    }

    public void getTaskCatalogueNeedAddId(Map<String, String> changeTableIds, List<TaskCatalogueDTO> taskCatalogueInfos, String tenantCode, String projectId) {
        taskCatalogueInfos.stream().forEach(taskCatalogueInfo -> {
            TaskCatalogueDTO taskCatalog = exportTaskMapper.getTaskCatalogExist(taskCatalogueInfo.getName(), tenantCode, projectId, taskCatalogueInfo.getParentId());
            if (null != taskCatalog) {
                changeTableIds.put(taskCatalogueInfo.getCatalogueId(), taskCatalog.getCatalogueId());
            } else {
                changeTableIds.put(taskCatalogueInfo.getCatalogueId(), Utils.getUUID());
            }
        });
    }

    public void getProcessNeedChange(Map<String, String> changeTableIds, List<ProcessExportDTO> list, Tuple2<Map<String, String>,
            Map<String, String>> tuple2, List<ImportExportTaskRecordDetail> saveTaskRecordDetails, String batchNo, String projectId) {
        Map<String, String> processDef = tuple2.getV1();
        Map<String, String> taskDef = tuple2.getV2();
        list.stream().forEach(processExportDTO -> {
            ProcessDefinition processDefinition = processExportDTO.getProcessDefinition();//这里是导入的
            if (processDef.containsKey(processDefinition.getName())) {
                changeTableIds.put(processDefinition.getCode(), processDef.get(processDefinition.getName()));
                saveTaskRecordDetails.add(new ImportExportTaskRecordDetail(batchNo, 2, processDefinition.getName(), 1, "覆盖", projectId));
            } else {
                if (changeTableIds.containsKey(processDefinition.getCode())) {
                    //处理调度任务
                    changeTableIds.put(processDefinition.getCode(), changeTableIds.get(processDefinition.getCode()));
                } else {
                    changeTableIds.put(processDefinition.getCode(), GeneratorUtil.genCode());
                }
                saveTaskRecordDetails.add(new ImportExportTaskRecordDetail(batchNo, 2, processDefinition.getName(), 1, "新增", projectId));
            }
            processExportDTO.getTaskDefinitionList().stream().forEach(task -> {
                if (taskDef.containsKey(task.getName())) {
                    changeTableIds.put(task.getCode(), taskDef.get(task.getName()));
                    saveTaskRecordDetails.add(new ImportExportTaskRecordDetail(batchNo, 1, task.getName(), 1, "覆盖", projectId));
                } else {
                    changeTableIds.put(task.getCode(), GeneratorUtil.genCode());
                    saveTaskRecordDetails.add(new ImportExportTaskRecordDetail(batchNo, 1, task.getName(), 1, "新增", projectId));
                }
            });
        });
    }

    public void getTaskCatalogueNeedChange(Map<String, String> changeTableIds, List<TaskCatalogueDTO> taskCatalogueInfos) {
        if (null != taskCatalogueInfos && taskCatalogueInfos.size() > 0) {
            taskCatalogueInfos.stream().forEach(t -> {
                if (t.getExistFlag() == 0) {
                    String catalogueId = Utils.getUUID();
                    changeTableIds.put(t.getCatalogueId(), catalogueId);
                    t.setCatalogueId(catalogueId);
                }
            });
        }
    }

    private List<TaskCatalogueDTO> getTaskCatalogueNeedChanage(List<TaskCatalogueDTO> taskCatalogueInfos, User user, String projectId, Map<String, String> changeTableIds) {
        List<TaskCatalogueDTO> result = new ArrayList<>();
        // 构建id->DTO映射，便于递归
        Map<String, TaskCatalogueDTO> idMap = taskCatalogueInfos.stream()
                .collect(Collectors.toMap(TaskCatalogueDTO::getCatalogueId, t -> t));
        // 找到所有根节点（parentId为0）
        List<TaskCatalogueDTO> roots = taskCatalogueInfos.stream()
                .filter(t -> "0".equals(t.getParentId()))
                .collect(Collectors.toList());
        for (TaskCatalogueDTO root : roots) {
            result.addAll(processNodeRecursive(root, idMap, user.getTenantCode(), projectId, "0", changeTableIds));
        }
        return result;
    }

    private List<TaskCatalogueDTO> processNodeRecursive(TaskCatalogueDTO node, Map<String, TaskCatalogueDTO> idMap, String tenantCode, String projectId, String parentId, Map<String, String> changeTableIds) {
        List<TaskCatalogueDTO> result = new ArrayList<>();

        // 保存原始的catalogueId，用于查找子节点
        String originalCatalogueId = node.getCatalogueId();

        // 创建节点副本，避免修改原始对象影响后续处理
        TaskCatalogueDTO processedNode = new TaskCatalogueDTO();
        BeanUtils.copyProperties(node, processedNode);

        // 查询目标端是否存在同名目录
        TempTaskCatalogue exist = exportTaskMapper.queryTaskCatalogueByName(processedNode.getName(), tenantCode, projectId, parentId);

        // 确定当前节点的新ID（如果存在则使用目标端ID，否则使用原ID）
        String newCatalogueId;
        if (exist != null) {
            changeTableIds.put(originalCatalogueId, exist.getCatalogueId());
            // 替换id为目标端id
            newCatalogueId = exist.getCatalogueId();
            processedNode.setCatalogueId(newCatalogueId);
            processedNode.setParentId(exist.getParentId());
            processedNode.setParentLevel(exist.getParentLevel());
            processedNode.setExistFlag(1);
            processedNode.setBeforeJson(JSONObject.toJSONString(processedNode));
        } else {
            newCatalogueId = originalCatalogueId;
            changeTableIds.put(originalCatalogueId, Utils.getUUID());
        }

        result.add(processedNode);

        // 递归处理子节点 - 使用原始ID来查找子节点
        for (TaskCatalogueDTO child : idMap.values()) {
            if (originalCatalogueId.equals(child.getParentId())) {
                // 传递新的ID作为子节点的parentId
                result.addAll(processNodeRecursive(child, idMap, tenantCode, projectId, newCatalogueId, changeTableIds));
            }
        }
        return result;
    }

    private Map<String, String> getTableIdNeedChange(List<TaskConfigInfoDTO> taskConfigInfos, Map<String, String> existTableId) {
        Map<String, String> map = new HashMap<>();
        taskConfigInfos.stream().forEach(taskConfigInfoDTO -> {
            if (StringUtils.isNotBlank(taskConfigInfoDTO.getPluginBasicsConfig())) {
                TablesChangesDTO tablesChangesDTO = JSONObject.parseObject(taskConfigInfoDTO.getPluginBasicsConfig(), TablesChangesDTO.class);
                if (StringUtils.isNotBlank(tablesChangesDTO.getDatasourceId()) && StringUtils.isNotBlank(tablesChangesDTO.getTableId()) && StringUtils.isNotBlank(tablesChangesDTO.getTableName())) {
                    //查询表信息,如果datasource 要替换的话，这里需要根据新的datasourceid查询
                    String datasourceId = existTableId.containsKey(tablesChangesDTO.getDatasourceId()) ? existTableId.get(tablesChangesDTO.getDatasourceId()) : tablesChangesDTO.getDatasourceId();
                    log.info("查询表信息 - 表名: {}, 表id: {}, datasourceId: {}", tablesChangesDTO.getTableName(), tablesChangesDTO.getTableId(), tablesChangesDTO.getDatasourceId());
                    String tableId = exportTaskMapper.queryTableExist(datasourceId, tablesChangesDTO.getTableName());
                    log.info("查询表信息 - 表名: {}, 表id: {}, datasourceId: {}", tablesChangesDTO.getTableName(), tableId, datasourceId);
                    //如果查询到就替换，没有查询到就替换为空
                    map.put(tablesChangesDTO.getTableId(), tableId == null ? "" : tableId);
                }
            }
        });
        return map;
    }

    private TempImport convertSccTempImport(List<ProcessExportDTO> sccInfos, String batchNo, User user, String projectId,
                                            Map<String, String> dataSourceMap, Map<String, String> tableMap) {
        TempImport tempImport = new TempImport();
        //Map<String, String> current = new HashMap<>(tableMap);
        String importJson = JSONObject.toJSONString(sccInfos);
        List<ProcessExportDTO> modifiedList = new ArrayList<>();
        for (ProcessExportDTO info : sccInfos) {
            ProcessExportDTO copy = new ProcessExportDTO();
            BeanUtils.copyProperties(info, copy); // 使用 Spring 或其他工具类拷贝对象
            copy.getProcessDefinition().setName(copy.getProcessDefinition().getName() + IMPORT_NAME);
            copy.getProcessDefinition().setId(copy.getProcessDefinition().getCode());
            //String pdCode = oldPds.get(copy.getProcessDefinition().getName());
            //current.put(copy.getProcessDefinition().getCode(), pdCode == null ? GeneratorUtil.genCode() : pdCode);
           /* copy.getTaskDefinitionList().stream().forEach(t -> {
                if (StringUtils.isNotEmpty(oldTaskIds.get(t.getName()))) {
                    current.put(t.getCode(), oldTaskIds.get(t.getName()));
                }
            });*/

            modifiedList.add(copy);
        }
        String modifiedJson = JSONObject.toJSONString(modifiedList);
        String afterJson = getAfterChangeJson(modifiedJson, dataSourceMap, tableMap);
        tempImport.setImportJson(importJson);
        tempImport.setBatchNo(batchNo);
        tempImport.setProjectId(projectId);
        tempImport.setAfterJson(afterJson);
        return tempImport;
    }

    private List<TempTaskConfigInfo> convertIntegrationTaskConfigInfo(List<TaskConfigInfoDTO> taskConfigInfos, String batchNo, User user, String projectId,
                                                                      Map<String, String> dataSourceMap, Map<String, String> tableMap) {
        List<TempTaskConfigInfo> taskConfigInfoList = new ArrayList<>();
        taskConfigInfos.stream().forEach(taskConfigInfo -> {
            TempTaskConfigInfo tmp = new TempTaskConfigInfo();
            BeanUtils.copyProperties(taskConfigInfo, tmp);
            tmp.setBatchNo(batchNo);
            tmp.setCreateBy(user.getUsername());
            tmp.setCreateTime(new Date());
            tmp.setUpdateBy(user.getUsername());
            tmp.setUpdateTime(new Date());
            tmp.setCreateUserId(user.getId());
            tmp.setCreateDeptId(user.getDeptId());
            tmp.setProjectId(projectId);
            tmp.setTenantId(user.getTenantCode());
            tmp.setAfterJson(getAfterChangeJson(JSONObject.toJSONString(tmp), dataSourceMap, tableMap));
            taskConfigInfoList.add(tmp);
        });
        return taskConfigInfoList;
    }

    private String getAfterChangeJson(String current, Map<String, String> dataSourceMap, Map<String, String> tableMap) {
        for (Map.Entry<String, String> entry : dataSourceMap.entrySet()) {
            current = current.replaceAll(Pattern.quote(entry.getKey()), Matcher.quoteReplacement(entry.getValue()));
        }
        for (Map.Entry<String, String> entry : tableMap.entrySet()) {
            current = current.replaceAll(Pattern.quote(entry.getKey()), Matcher.quoteReplacement(entry.getValue()));
        }
        return current;
    }

    private List<TempTaskConfigLineInfo> convertIntegrationTaskConfigLineInfo(List<TaskConfigLineInfoDTO> taskConfigLineInfos, String batchNo, User user, String projectId,
                                                                              Map<String, String> dataSourceMap, Map<String, String> changeTableIds) {
        List<TempTaskConfigLineInfo> taskConfigLineInfoList = new ArrayList<>();
        taskConfigLineInfos.stream().forEach(taskConfigLineInfo -> {
            TempTaskConfigLineInfo tmp = new TempTaskConfigLineInfo();
            BeanUtils.copyProperties(taskConfigLineInfo, tmp);
            tmp.setBatchNo(batchNo);
            tmp.setCreateBy(user.getUsername());
            tmp.setCreateTime(new Date());
            tmp.setUpdateBy(user.getUsername());
            tmp.setUpdateTime(new Date());
            tmp.setCreateUserId(user.getId());
            tmp.setCreateDeptId(user.getDeptId());
            tmp.setProjectId(projectId);
            tmp.setTenantId(user.getTenantCode());
            TempTaskConfigLineInfo after = new TempTaskConfigLineInfo();
            BeanUtils.copyProperties(tmp, after);
            after.setLineInfoId(Utils.getUUID());
            after.setUpdateBy(user.getUsername());
            tmp.setAfterJson(getAfterChangeJson(JSONObject.toJSONString(after), dataSourceMap, changeTableIds));
            taskConfigLineInfoList.add(tmp);
        });
        return taskConfigLineInfoList;
    }

    private List<TempTaskVersionInfo> convertIntegrationTaskVersionInfo(List<TaskVersionInfoDTO> taskVersionInfos, String batchNo, User user, String projectId,
                                                                        Map<String, String> dataSourceMap, Map<String, String> changeTableIds) {
        List<TempTaskVersionInfo> taskVersionInfoList = new ArrayList<>();
        //Map<String, String> current = new HashMap<>(changeTableIds);
        taskVersionInfos.stream().forEach(taskVersionInfo -> {
            TempTaskVersionInfo tmp = new TempTaskVersionInfo();
            BeanUtils.copyProperties(taskVersionInfo, tmp);
            Date date = new Date();
            tmp.setBatchNo(batchNo);
            tmp.setCreateBy(user.getUsername());
            tmp.setCreateTime(new Date());
            tmp.setUpdateBy(user.getUsername());
            tmp.setUpdateTime(date);
            tmp.setCreateUserId(user.getId());

            tmp.setCreateDeptId(user.getDeptId());
            tmp.setProjectId(projectId);
            tmp.setTenantId(user.getTenantCode());
            tmp.setSubmitTime(date);
            TempTaskVersionInfo after = new TempTaskVersionInfo();
            BeanUtils.copyProperties(tmp, after);
            after.setProcessName(after.getProcessName() + IMPORT_NAME);
            //String oldId = oldPds.get(after.getProcessName());
            //current.put(after.getProcessDefinitionId(), oldId == null ? after.getProcessDefinitionId() : oldId);
            after.setTaskName(after.getTaskName() + IMPORT_NAME);
            //String oldTaskId = oldTaskIds.get(after.getTaskName());
            //current.put(after.getTaskId(), oldTaskId == null ? after.getTaskId() : oldTaskId);
            tmp.setAfterJson(getAfterChangeJson(JSONObject.toJSONString(after), dataSourceMap, changeTableIds));
            taskVersionInfoList.add(tmp);
        });
        return taskVersionInfoList;
    }

    private List<TempDiProcessDefinition> convertIntegrationDiProcessDefinition(List<DiProcessDefinitionDTO> diProcessDefinition, String batchNo, User user, String projectId,
                                                                                Map<String, String> dataSourceMap, Map<String, String> changeTableIds) {
        List<TempDiProcessDefinition> list = new ArrayList<>();
        if (null != diProcessDefinition && diProcessDefinition.size() > 0) {
            diProcessDefinition.stream().forEach(pd -> {
                TempDiProcessDefinition tmp = new TempDiProcessDefinition();
                BeanUtils.copyProperties(pd, tmp);
                tmp.setTenantCode(user.getTenantCode());
                tmp.setProjectId(projectId);
                tmp.setBatchNo(batchNo);
                tmp.setCreateTime(new Date());
                tmp.setLastModificationTime(new Date());
                tmp.setCreateBy(user.getUsername());
                tmp.setCreateByName(user.getUsername());
                tmp.setUpdateBy(user.getUsername());
                tmp.setDataOwnerDeptId(user.getDeptId());
                tmp.setDataOwnerDeptName(user.getDeptName());
                tmp.setDataOwnerUserId(user.getId());
                tmp.setDataOwnerUserName(user.getUsername());
                TempDiProcessDefinition after = new TempDiProcessDefinition();
                BeanUtils.copyProperties(tmp, after);
                tmp.setAfterJson(getAfterChangeJson(JSONObject.toJSONString(after), dataSourceMap, changeTableIds));
                list.add(tmp);
            });
        }
        return list;
    }

    private List<TempDiProcessTaskRelation> convertIntegrationDiProcessTaskRelation(List<DiProcessTaskRelationDTO> diProcessTaskRelations, String batchNo, User user, String projectId,
                                                                                    Map<String, String> dataSourceMap, Map<String, String> changeTableIds) {
        List<TempDiProcessTaskRelation> list = new ArrayList<>();
        if (null != diProcessTaskRelations && diProcessTaskRelations.size() > 0) {
            diProcessTaskRelations.stream().forEach(pt -> {
                TempDiProcessTaskRelation tmp = new TempDiProcessTaskRelation();
                BeanUtils.copyProperties(pt, tmp);
                tmp.setTenantCode(user.getTenantCode());
                tmp.setProjectId(projectId);
                tmp.setBatchNo(batchNo);
                tmp.setCreateTime(new Date());
                tmp.setLastModificationTime(new Date());
                tmp.setCreateBy(user.getUsername());
                tmp.setCreateByName(user.getUsername());
                tmp.setUpdateBy(user.getUsername());
                tmp.setDataOwnerDeptId(user.getDeptId());
                tmp.setDataOwnerDeptName(user.getDeptName());
                tmp.setDataOwnerUserId(user.getId());
                tmp.setDataOwnerUserName(user.getUsername());
                TempDiProcessTaskRelation after = new TempDiProcessTaskRelation();
                BeanUtils.copyProperties(tmp, after);
                tmp.setAfterJson(getAfterChangeJson(JSONObject.toJSONString(after), dataSourceMap, changeTableIds));
                list.add(tmp);
            });
        }
        return list;
    }

    private List<TempDiScheduler> convertIntegrationDiScheduler(List<DiSchedulerDTO> diScheduler, String batchNo, User user, String projectId,
                                                                Map<String, String> dataSourceMap, Map<String, String> changeTableIds) {
        List<TempDiScheduler> list = new ArrayList<>();
        if (null != diScheduler && diScheduler.size() > 0) {
            diScheduler.stream().forEach(ds -> {
                TempDiScheduler tmp = new TempDiScheduler();
                BeanUtils.copyProperties(ds, tmp);
                tmp.setTenantCode(user.getTenantCode());
                tmp.setProjectId(projectId);
                tmp.setBatchNo(batchNo);
                tmp.setCreateTime(new Date());
                tmp.setLastModificationTime(new Date());
                tmp.setCreateBy(user.getUsername());
                tmp.setCreateByName(user.getUsername());
                tmp.setUpdateBy(user.getUsername());
                tmp.setDataOwnerDeptId(user.getDeptId());
                tmp.setDataOwnerDeptName(user.getDeptName());
                tmp.setDataOwnerUserId(user.getId());
                tmp.setDataOwnerUserName(user.getUsername());
                TempDiScheduler after = new TempDiScheduler();
                BeanUtils.copyProperties(tmp, after);
                tmp.setAfterJson(getAfterChangeJson(JSONObject.toJSONString(after), dataSourceMap, changeTableIds));
                list.add(tmp);
            });
        }
        return list;
    }

    private List<TempTaskCatalogue> convertIntegrationTaskCatalogue(List<TaskCatalogueDTO> taskCatalogueInfos, String batchNo, User user, String projectId,
                                                                    Map<String, String> dataSourceMap, Map<String, String> changeTableIds) {
        List<TempTaskCatalogue> taskCatalogueInfoList = new ArrayList<>();
        //Map<String, String> current = new HashMap<>(changeTableIds);
        taskCatalogueInfos.stream().forEach(taskCatalogueInfo -> {
            TempTaskCatalogue tmp = new TempTaskCatalogue();
            BeanUtils.copyProperties(taskCatalogueInfo, tmp);
            tmp.setBatchNo(batchNo);
            tmp.setCreateBy(user.getUsername());
            tmp.setCreateTime(new Date());
            tmp.setUpdateBy(user.getUsername());
            tmp.setUpdateTime(new Date());
            tmp.setCreateUserId(user.getId());
            tmp.setCreateDeptId(user.getDeptId());
            tmp.setProjectId(projectId);
            tmp.setTenantId(user.getTenantCode());
            TempTaskCatalogue after = new TempTaskCatalogue();
            BeanUtils.copyProperties(tmp, after);
            /*if (StringUtils.isNotEmpty(tmp.getTaskId()) && StringUtils.isNotEmpty(tmp.getName())) {
                String oldTaskId = oldTaskIds.get(tmp.getName());
                current.put(tmp.getTaskId(), oldTaskId == null ? tmp.getTaskId() : oldTaskId);
            }*/
            tmp.setAfterJson(getAfterChangeJson(JSONObject.toJSONString(after), dataSourceMap, changeTableIds));
            taskCatalogueInfoList.add(tmp);
        });
        return taskCatalogueInfoList;
    }

    private List<TempTaskDefinitionInfo> convertIntegrationTaskDefinitionInfo(List<TaskDefinitionInfoDTO> taskDefinitionInfos, String batchNo, User user, String projectId,
                                                                              Map<String, String> dataSourceMap, Map<String, String> changeTableIds) {
        List<TempTaskDefinitionInfo> taskDefinitionInfoList = new ArrayList<>();
        //Map<String, String> current = new HashMap<>(changeTableIds);
        taskDefinitionInfos.stream().forEach(taskDefinitionInfo -> {
            TempTaskDefinitionInfo tmp = new TempTaskDefinitionInfo();
            BeanUtils.copyProperties(taskDefinitionInfo, tmp);
            tmp.setBatchNo(batchNo);
            tmp.setCreateBy(user.getUsername());
            tmp.setCreateTime(new Date());
            tmp.setUpdateBy(user.getUsername());
            tmp.setUpdateTime(new Date());
            tmp.setTmpVersionId(tmp.getCurrentVersionId());
            tmp.setCreateUserId(user.getId());
            tmp.setCreateDeptId(user.getDeptId());
            tmp.setProjectId(projectId);
            tmp.setTenantId(user.getTenantCode());
            TempTaskDefinitionInfo after = new TempTaskDefinitionInfo();
            BeanUtils.copyProperties(tmp, after);
            //String oldId = oldTaskIds.get(after.getTaskName());
            //current.put(after.getTaskId(), oldId == null ? after.getTaskId() : oldId);
            after.setTaskName(after.getTaskName() + IMPORT_NAME);
            tmp.setAfterJson(getAfterChangeJson(JSONObject.toJSONString(after), dataSourceMap, changeTableIds));
            taskDefinitionInfoList.add(tmp);
        });
        return taskDefinitionInfoList;
    }

    private List<TempDatasourceBusiness> convertDatasourceBusiness(List<DatasourceBusinessDTO> allList, String batchNo, User user, String projectId) {
        List<TempDatasourceBusiness> list = new ArrayList<>();
        allList.stream().forEach(business -> {
            TempDatasourceBusiness tmp = new TempDatasourceBusiness();
            BeanUtils.copyProperties(business, tmp);
            tmp.setBatchNo(batchNo);
            tmp.setCreateBy(user.getUsername());
            tmp.setCreateTime(new Date());
            tmp.setUpdateBy(user.getUsername());
            tmp.setUpdateTime(new Date());
            tmp.setCreateUserId(user.getId());
            tmp.setCreateDeptId(user.getDeptId());
            tmp.setProjectId(projectId);
            tmp.setTenantId(user.getTenantCode());
            tmp.setAfterJson(JSONObject.toJSONString(tmp));
            list.add(tmp);
        });
        return list;
    }

    private List<TempDatasourceReference> convertDatasourceReference(List<DatasourceReferenceDTO> referenceDTOS, String batchNo, User user) {
        List<TempDatasourceReference> list = new ArrayList<>();
        referenceDTOS.stream().forEach(reference -> {
            TempDatasourceReference tmp = new TempDatasourceReference();
            BeanUtils.copyProperties(reference, tmp);
            tmp.setBatchNo(batchNo);
            tmp.setAfterJson(JSONObject.toJSONString(tmp));
            list.add(tmp);
        });
        return list;
    }

    private List<TempDatasourceInfoAuthorizationOperation> convertDatasourceAuthorizationOperation(List<DatasourceInfoAuthorizationOperationDTO> operationDTOS, String batchNo, User user) {
        List<TempDatasourceInfoAuthorizationOperation> list = new ArrayList<>();
        operationDTOS.stream().forEach(operation -> {
            TempDatasourceInfoAuthorizationOperation tmp = new TempDatasourceInfoAuthorizationOperation();
            BeanUtils.copyProperties(operation, tmp);
            tmp.setBatchNo(batchNo);
            tmp.setCreateBy(user.getUsername());
            tmp.setCreateTime(new Date());
            tmp.setUpdateBy(user.getUsername());
            tmp.setCreateUserId(user.getId());
            tmp.setCreateDeptId(user.getDeptId());
            tmp.setTenantId(user.getTenantCode());
            tmp.setAfterJson(JSONObject.toJSONString(tmp));
            list.add(tmp);
        });
        return list;
    }

    private List<TempDatasourceInfoAuthorization> convertDatasourceAuthorization(List<DatasourceInfoAuthorizationDTO> authorizationDTOS, String batchNo, User user) {
        List<TempDatasourceInfoAuthorization> list = new ArrayList<>();
        authorizationDTOS.stream().forEach(authorization -> {
            TempDatasourceInfoAuthorization tmp = new TempDatasourceInfoAuthorization();
            BeanUtils.copyProperties(authorization, tmp);
            tmp.setBatchNo(batchNo);
            tmp.setCreateBy(user.getUsername());
            tmp.setCreateTime(new Date());
            tmp.setUpdateBy(user.getUsername());
            tmp.setUpdateTime(new Date());
            tmp.setCreateUserId(user.getId());
            tmp.setCreateDeptId(user.getDeptId());
            tmp.setTenantId(user.getTenantCode());
            tmp.setAfterJson(JSONObject.toJSONString(tmp));
            list.add(tmp);
        });
        return list;
    }

    private List<TempDatasourceInfo> convertDatasource(List<DatasourceInfoDTO> allList, String batchNo, User user, String projectId, Map<String, String> uuids) {
        List<TempDatasourceInfo> list = new ArrayList<>();
        allList.stream().forEach(datasource -> {
            TempDatasourceInfo tmp = new TempDatasourceInfo();
            BeanUtils.copyProperties(datasource, tmp);
            tmp.setBatchNo(batchNo);
            tmp.setCreateBy(user.getUsername());
            tmp.setCreateTime(new Date());
            tmp.setUpdateBy(user.getUsername());
            tmp.setUpdateTime(new Date());
            tmp.setCreateUserId(user.getId());
            tmp.setCreateDeptId(user.getDeptId());
            tmp.setProjectId(projectId);
            tmp.setTenantId(user.getTenantCode());
            tmp.setFirstAuthorizationTime(new Date());
            tmp.setNewAuthorizationTime(new Date());
            TempDatasourceInfo after = new TempDatasourceInfo();
            BeanUtils.copyProperties(tmp, after);
            tmp.setAfterJson(getAfterChangeJson(JSONObject.toJSONString(after), uuids, new HashMap<>()));
            list.add(tmp);
        });
        return list;
    }

    private Tuple2<List<DatasourceBusinessDTO>, Map<String, String>> checkDatasourceBusinessExist(List<DatasourceBusinessDTO> datasourceBusiness, String tenantCode) {
        List<DatasourceBusinessDTO> list = new ArrayList<>();
        Map<String, String> needChangeInfo = new HashMap<>();
        datasourceBusiness.stream().forEach(datasourceBusinessDTO -> {
            DatasourceBusinessDTO exist = exportTaskMapper.queryDatasourceBusinessByName(datasourceBusinessDTO.getBusinessName(), tenantCode);
            log.info("数据源所属系统 {} ,租户code {},是否存在 {}", datasourceBusinessDTO.getBusinessName(), tenantCode, null != exist);
            if (null != exist) {
                needChangeInfo.put(datasourceBusinessDTO.getUuid(), exist.getUuid());
                datasourceBusinessDTO.setExistFlag(1);
                datasourceBusinessDTO.setBeforeJson(JSONObject.toJSONString(exist));
            } else {
                datasourceBusinessDTO.setExistFlag(0);
            }
            list.add(datasourceBusinessDTO);
        });
        return Tuple2.tuple(list, needChangeInfo);
    }

    /**
     * 检查任务目录路径是否完全存在，如果有一层不存在则返回null，如果都存在则返回最后一层的task_id
     *
     * @param taskCatalogues 任务目录列表（老的树结构数据）
     * @param tenantCode     租户代码
     * @param needChangeIds  用于记录旧ID到新ID的映射关系（key=旧catalogue_id, value=新catalogue_id）
     * @return 如果路径完全存在返回最后一层的task_id，否则返回null
     */
    public List<String> checkTaskAlreadyExist(List<TaskCatalogueDTO> taskCatalogues, String tenantCode, Map<String, String> needChangeIds) {
        if (taskCatalogues == null || taskCatalogues.isEmpty()) {
            return null;
        }

        // 1. 构建目录树结构
        Map<String, TaskCatalogueDTO> catalogueMap = new HashMap<>();
        for (TaskCatalogueDTO catalogue : taskCatalogues) {
            catalogueMap.put(catalogue.getCatalogueId(), catalogue);
        }

        // 2. 构建有序的目录路径（从根到叶子）
        List<TaskCatalogueDTO> orderedPath = buildOrderedPath(taskCatalogues, catalogueMap);

        if (orderedPath.isEmpty()) {
            log.warn("无法构建有效的目录路径");
            return null;
        }

        // 3. 从顶层开始逐级检查
        String currentParentId = "0";
        List<String> ids = new ArrayList<>();

        for (int i = 0; i < orderedPath.size(); i++) {
            TaskCatalogueDTO catalogue = orderedPath.get(i);

            log.info("检查目录层级 {}/{} - 名称: {}, 父目录ID: {}, 旧ID: {}",
                    i + 1, orderedPath.size(), catalogue.getName(), currentParentId, catalogue.getCatalogueId());

            // 查询当前层级是否存在
            TaskCatalogueDTO existingCatalogue = queryExistingCatalogue(catalogue.getName(), currentParentId, tenantCode);

            if (null != existingCatalogue) {
                //说明存在，则继续查询
                log.info("目录存在 - 名称: {}, 旧ID: {}, 新ID: {}",
                        catalogue.getName(), catalogue.getCatalogueId(), existingCatalogue.getCatalogueId());

                // 记录旧ID到新ID的映射关系
                needChangeIds.put(catalogue.getCatalogueId(), existingCatalogue.getCatalogueId());
                log.debug("记录ID映射 - 旧ID: {} -> 新ID: {}", catalogue.getCatalogueId(), existingCatalogue.getCatalogueId());

                // 更新父目录ID为当前找到的目录ID，用于下一层查询
                currentParentId = existingCatalogue.getCatalogueId();

                // 如果当前目录有taskId，记录下来（最后一层的taskId会被保留）
                if (StringUtils.isNotBlank(existingCatalogue.getTaskId())) {
                    ids.add(existingCatalogue.getTaskId());
                }

            } else {
                // 有一层不存在，直接返回null
                log.info("目录路径中断 - 第{}层目录不存在: {}, 旧ID: {}", i + 1, catalogue.getName(), catalogue.getCatalogueId());
                return null;
            }
        }

        return ids;
    }


    /**
     * 构建有序的目录路径（从根到叶子）
     *
     * @param taskCatalogues 任务目录列表
     * @param catalogueMap   目录映射
     * @return 有序的目录路径
     */
    private List<TaskCatalogueDTO> buildOrderedPath(List<TaskCatalogueDTO> taskCatalogues,
                                                    Map<String, TaskCatalogueDTO> catalogueMap) {
        List<TaskCatalogueDTO> orderedPath = new ArrayList<>();

        // 找到最深层的目录（parentLevel最长的）
        TaskCatalogueDTO deepestCatalogue = null;
        int maxLevels = 0;

        for (TaskCatalogueDTO catalogue : taskCatalogues) {
            if (StringUtils.isNotBlank(catalogue.getParentLevel())) {
                String[] levels = catalogue.getParentLevel().split(",");
                if (levels.length > maxLevels) {
                    maxLevels = levels.length;
                    deepestCatalogue = catalogue;
                }
            }
        }

        if (deepestCatalogue == null) {
            log.warn("未找到有效的目录层级结构");
            return orderedPath;
        }

        log.info("找到最深层目录: {}, parentLevel: {}", deepestCatalogue.getName(), deepestCatalogue.getParentLevel());

        // 根据最深层目录的parentLevel构建完整路径
        String[] levelIds = deepestCatalogue.getParentLevel().split(",");

        for (String levelId : levelIds) {
            String trimmedLevelId = levelId.trim();
            TaskCatalogueDTO levelCatalogue = catalogueMap.get(trimmedLevelId);
            if (levelCatalogue != null) {
                orderedPath.add(levelCatalogue);
                log.debug("添加到路径: {} (ID: {})", levelCatalogue.getName(), trimmedLevelId);
            } else {
                log.warn("在catalogueMap中找不到目录ID: {}", trimmedLevelId);
            }
        }

        log.info("构建的目录路径: {}", orderedPath.stream()
                .map(catalogue -> catalogue.getName() + "(" + catalogue.getCatalogueId() + ")")
                .collect(Collectors.joining(" -> ")));

        return orderedPath;
    }

    /**
     * 查询指定名称和父目录下的目录信息
     *
     * @param name       目录名称
     * @param parentId   父目录ID
     * @param tenantCode 租户代码
     * @return 存在的目录信息，不存在返回null
     */
    private TaskCatalogueDTO queryExistingCatalogue(String name, String parentId, String tenantCode) {
        try {
            log.debug("查询目录 - 名称: {}, 父目录ID: {}, 租户: {}", name, parentId, tenantCode);

            TaskCatalogueDTO existingCatalogues = exportTaskMapper.queryTaskCatalogueByNameAndParent(name, parentId, tenantCode);

            if (null != existingCatalogues) {
                return existingCatalogues;
            }

            log.debug("目录不存在 - 名称: {}, 父目录ID: {}", name, parentId);
            return null;

        } catch (Exception e) {
            log.error("查询目录失败 - 名称: {}, 父目录ID: {}, 租户: {}, 错误: {}",
                    name, parentId, tenantCode, e.getMessage());
            return null;
        }
    }


    /**
     * 获取目录的完整路径（用于调试和日志）
     *
     * @param catalogue    目录对象
     * @param catalogueMap 目录映射
     * @return 完整路径
     */
    private String getCatalogueFullPath(TaskCatalogueDTO catalogue, Map<String, TaskCatalogueDTO> catalogueMap) {
        List<String> pathParts = new ArrayList<>();
        TaskCatalogueDTO current = catalogue;

        while (current != null && !"0".equals(current.getParentId())) {
            pathParts.add(0, current.getName());
            current = catalogueMap.get(current.getParentId());
        }

        if (current != null) {
            pathParts.add(0, current.getName());
        }

        return String.join("/", pathParts);
    }

    public List<DatasourceReferenceDTO> checkDatasourceReferenceExist(List<String> datasourceIds, String projectId, String productId) {
        List<DatasourceReferenceDTO> list = new ArrayList<>();
        datasourceIds.stream().forEach(datasourceId -> {
            DatasourceReferenceDTO exist = exportTaskMapper.queryDatasourceReferenceByDatasourceId(datasourceId, productId, projectId);
            log.info("数据源引用 {} ,项目id {},产品id {},是否存在 {}", datasourceId, projectId, productId, null != exist);
            if (null != exist) {
                exist.setExistFlag(1);
                exist.setBeforeJson(JSONObject.toJSONString(exist));
                list.add(exist);
            } else {
                DatasourceReferenceDTO referenceDTO = new DatasourceReferenceDTO();
                referenceDTO.setDatasourceInfoId(datasourceId);
                referenceDTO.setExistFlag(0);
                referenceDTO.setDataType("project");
                referenceDTO.setProductId(productId);
                referenceDTO.setItemId(projectId);
                list.add(referenceDTO);
            }
        });
        return list;
    }

    private List<DatasourceInfoAuthorizationOperationDTO> checkDatasourceAuthorizationOperationExist(List<String> datasourceIds, String projectId, String productId) {
        List<DatasourceInfoAuthorizationOperationDTO> list = new ArrayList<>();
        datasourceIds.stream().forEach(datasourceId -> {
            DatasourceInfoAuthorizationOperationDTO exist = exportTaskMapper.queryDatasourceInfoAuthorizationOperationByDatasourceId(datasourceId, productId, projectId);
            log.info("数据源授权操作 {} ,项目id {},产品id {},是否存在 {}", datasourceId, projectId, productId, null != exist);
            if (null != exist) {
                exist.setExistFlag(1);
                exist.setBeforeJson(JSONObject.toJSONString(exist));
                list.add(exist);
            } else {
                DatasourceInfoAuthorizationOperationDTO operationDTO = new DatasourceInfoAuthorizationOperationDTO();
                operationDTO.setDatasourceInfoId(datasourceId);
                operationDTO.setDataType("project");
                operationDTO.setProductId(productId);
                operationDTO.setProjectId(projectId);
                operationDTO.setItemId(projectId);
                operationDTO.setOperationType("insert");
                operationDTO.setExistFlag(0);
                list.add(operationDTO);
            }
        });
        return list;
    }

    private List<DatasourceInfoAuthorizationDTO> checkDatasourceAuthorizationExist(
            List<String> datasourceIds, String projectId, String productId) {
        List<DatasourceInfoAuthorizationDTO> list = new ArrayList<>();
        datasourceIds.stream().forEach(datasourceId -> {
            DatasourceInfoAuthorizationDTO exist = exportTaskMapper.queryDatasourceInfoAuthorizationByDatasourceId(datasourceId, productId, projectId);
            log.info("数据源授权 {} ,项目id {},产品id {},是否存在 {}", datasourceId, projectId, productId, null != exist);
            if (null != exist) {
                exist.setExistFlag(1);
                exist.setBeforeJson(JSONObject.toJSONString(exist));
                list.add(exist);
            } else {
                DatasourceInfoAuthorizationDTO authorization = new DatasourceInfoAuthorizationDTO();
                authorization.setDatasourceInfoId(datasourceId);
                authorization.setExistFlag(0);
                authorization.setDataType("product");
                authorization.setProductId(productId);
                authorization.setProjectId(projectId);
                authorization.setType("product");
                list.add(authorization);

            }
        });
        return list;
    }

    private Tuple2<List<DatasourceInfoDTO>, Map<String, String>> checkDatasourceExist(List<DatasourceInfoDTO> datasource, String tenantCode,
                                                                                      Map<String, String> needChangeInfos,
                                                                                      List<ImportExportTaskRecordDetail> saveTaskRecordDetails, String batchNo, String projectId) {
        List<DatasourceInfoDTO> list = new ArrayList<>();
        //key 是旧的id，value是新的id
        datasource.stream().forEach(datasourceInfoDTO -> {
            ImportExportTaskRecordDetail detail;
            DatasourceInfoDTO exist = exportTaskMapper.queryDatasourceInfoByName(datasourceInfoDTO.getDataName(), tenantCode);
            log.info("数据源 {} ,租户code {},是否存在 {}", datasourceInfoDTO.getDataName(), tenantCode, null != exist);
            if (null != exist) {
                //替换id
                needChangeInfos.put(datasourceInfoDTO.getDatasourceInfoId(), exist.getDatasourceInfoId());
                //替换ip
                needChangeInfos.put(datasourceInfoDTO.getIp(), exist.getIp());
                datasourceInfoDTO.setExistFlag(1);
                datasourceInfoDTO.setBeforeJson(JSONObject.toJSONString(exist));
                detail = new ImportExportTaskRecordDetail(batchNo, 0, exist.getDataName(), 1, "跳过", projectId);
            } else {
                datasourceInfoDTO.setExistFlag(0);
                detail = new ImportExportTaskRecordDetail(batchNo, 0, datasourceInfoDTO.getDataName(), 1, "新增", projectId);
            }
            saveTaskRecordDetails.add(detail);
            list.add(datasourceInfoDTO);
        });
        return Tuple2.tuple(list, needChangeInfos);
    }

    /**
     * 同步数据，将临时表数据同步到正式表
     *
     * @param batchNo   批次号
     * @param projectId 项目ID
     * @param productId 产品ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void sync(String batchNo, String projectId, String productId) {
        log.info("[BatchNo:{}] 开始同步临时表数据到正式表，项目：{}，产品：{}", batchNo, projectId, productId);
        try {
            log.info("开始清理数据");
            ImportExportTaskRecord importExportTaskRecord = importExportTaskRecordService.getQuery().eq("batchNo", batchNo).one();
            if (null != importExportTaskRecord && StringUtils.isNotEmpty(importExportTaskRecord.getBeforeJson())) {
                List<IntegrationDTO> list = JSONObject.parseArray(importExportTaskRecord.getBeforeJson(), IntegrationDTO.class);
                list.stream().forEach(integrationDTO -> {
                    Tuple2<Map<String, String>, Map<String, String>> deleteTaskIds = getProcessDefinitionNeedDelete(integrationDTO.getSccInfos(), projectId);
                    Map<String, String> processDefinitionMaps = deleteTaskIds.getV1();
                    if (null != processDefinitionMaps.values() && processDefinitionMaps.values().size() > 0) {
                        processDefinitionMaps.values().forEach(processDefinitionId -> {
                            deleteProcess(processDefinitionId);
                        });
                    } else {
                        Map<String, List<String>> deleteInfors = integrationDTO.getDeleteProcessInfos();
                        String processCode = "processCode";
                        String taskCode = "taskCode";
                        if (deleteInfors.containsKey(processCode)) {
                            List<String> del = deleteInfors.get(processCode);
                            del.stream().forEach(id -> {
                                deleteProcess(id);
                            });
                        }
                        if (deleteInfors.containsKey(taskCode)) {
                            List<String> taskCodes = deleteInfors.get(taskCode);
                            deleteTask(taskCodes);
                        }
                    }
                    Map<String, String> taskIds = deleteTaskIds.getV2();
                    if (null != taskIds.values() && taskIds.values().size() > 0) {
                        List<String> ids = taskIds.values().stream().collect(Collectors.toList());
                        deleteTask(ids);
                    }

                });
            }
            // 1. 同步数据源所属系统
            log.info("[BatchNo:{}] 开始同步数据源所属系统", batchNo);
            List<TempDatasourceBusiness> notExistDatasourceBusinessLists = exportTaskMapper.queryDatasourceBusinessByBatchNoFlag(batchNo, 0);
            if (null != notExistDatasourceBusinessLists && notExistDatasourceBusinessLists.size() > 0) {
                List<TempDatasourceBusiness> notExistDatasourceBusinessList = initDatasourceBusinessLists(notExistDatasourceBusinessLists);
                exportTaskMapper.insertDatasoruceBusiness(notExistDatasourceBusinessList);
                log.info("[BatchNo:{}] 数据源所属系统同步完成，数量：{}", batchNo, notExistDatasourceBusinessList.size());
            } else {
                log.info("[BatchNo:{}] 没有数据源所属系统需要同步", batchNo);
            }
            // 2. 同步数据源
            log.info("[BatchNo:{}] 开始同步数据源", batchNo);
            List<TempDatasourceInfo> notExistDatasourceLists = exportTaskMapper.queryDatasourceInfoByBatchNoFlag(batchNo, 0);
            if (null != notExistDatasourceLists && notExistDatasourceLists.size() > 0) {
                List<TempDatasourceInfo> newDatasourceLists = initDatasourceLists(notExistDatasourceLists);
                exportTaskMapper.insertDatasoruces(newDatasourceLists);
                log.info("[BatchNo:{}] 数据源同步完成，数量：{}", batchNo, newDatasourceLists.size());
            } else {
                log.info("[BatchNo:{}] 没有数据源需要同步", batchNo);
            }
            log.info("[BatchNo:{}] 同步数据源授权信息：{}", batchNo, "数据源");
            List<TempDatasourceInfoAuthorization> notExistDatasourceAuthorizationLists = exportTaskMapper.queryDatasourceInfoAuthorizationByBatchNoFlag(batchNo, 0);
            if (null != notExistDatasourceAuthorizationLists && notExistDatasourceAuthorizationLists.size() > 0) {
                List<TempDatasourceInfoAuthorization> newDatasourceAuthorizationLists = initDatasourceAuthorizationLists(notExistDatasourceAuthorizationLists);
                exportTaskMapper.insertDatasourceInfoAuthorizations(newDatasourceAuthorizationLists);
            }
            log.info("[BatchNo:{}] 同步数据源授权操作信息：{}", batchNo, "数据源授权");
            List<TempDatasourceInfoAuthorizationOperation> notExistDatasourceInfoAuthorizationOperationLists = exportTaskMapper.queryDatasourceInfoAuthorizationOperationByBatchNoFlag(batchNo, 0);
            if (null != notExistDatasourceInfoAuthorizationOperationLists && notExistDatasourceInfoAuthorizationOperationLists.size() > 0) {
                List<TempDatasourceInfoAuthorizationOperation> newDatasourceInfoAuthorizationOperationLists = initDatasourceInfoAuthorizationOperationLists(notExistDatasourceInfoAuthorizationOperationLists);
                exportTaskMapper.insertDatasourceInfoAuthorizationOperations(newDatasourceInfoAuthorizationOperationLists);
            }
            log.info("[BatchNo:{}] 同步数据源引入：{}", batchNo, "数据源引入");
            List<TempDatasourceReference> notExistDatasourceReferenceLists = exportTaskMapper.queryDatasourceReferenceByBatchNoFlag(batchNo, 0);
            if (null != notExistDatasourceReferenceLists && notExistDatasourceReferenceLists.size() > 0) {
                List<TempDatasourceReference> newDatasourceReferenceLists = initDatasourceReferenceLists(notExistDatasourceReferenceLists);
                exportTaskMapper.insertDatasourceReferences(newDatasourceReferenceLists);
            }
            // 3. 同步任务目录
            log.info("[BatchNo:{}] 开始同步任务目录", batchNo);
            List<TempTaskCatalogue> notExistTaskCatalogueLists = exportTaskMapper.queryTaskCatalogueByBatchNoFlag(batchNo, 0);
            if (null != notExistTaskCatalogueLists && notExistTaskCatalogueLists.size() > 0) {
                List<TempTaskCatalogue> newTaskCatalogueLists = initTaskCatalogueLists(notExistTaskCatalogueLists);
                exportTaskMapper.insertTaskCatalogues(newTaskCatalogueLists);
                log.info("[BatchNo:{}] 任务目录同步完成，数量：{}", batchNo, newTaskCatalogueLists.size());
            } else {
                log.info("[BatchNo:{}] 没有任务目录需要同步", batchNo);
            }
            // 4. 同步任务配置信息
            log.info("[BatchNo:{}] 开始同步任务配置信息", batchNo);
            List<TempTaskConfigInfo> notExistTaskConfigInfoLists = exportTaskMapper.queryTaskConfigInfoByBatchNo(batchNo);
            if (null != notExistTaskConfigInfoLists && notExistTaskConfigInfoLists.size() > 0) {
                List<TempTaskConfigInfo> newTaskConfigInfoLists = initTaskConfigInfoLists(notExistTaskConfigInfoLists);
                exportTaskMapper.insertTaskConfigInfos(newTaskConfigInfoLists);
                log.info("[BatchNo:{}] 任务配置信息同步完成，数量：{}", batchNo, newTaskConfigInfoLists.size());
            } else {
                log.info("[BatchNo:{}] 没有任务配置信息需要同步", batchNo);
            }

            // 5. 同步任务配置行信息
            log.info("[BatchNo:{}] 开始同步任务配置行信息", batchNo);
            List<TempTaskConfigLineInfo> notExistTaskConfigLineInfoLists = exportTaskMapper.queryTaskConfigLineInfoByBatchNo(batchNo);
            if (null != notExistTaskConfigLineInfoLists && notExistTaskConfigLineInfoLists.size() > 0) {
                List<TempTaskConfigLineInfo> newTaskConfigLineInfoLists = initTaskConfigLineInfoLists(notExistTaskConfigLineInfoLists);
                exportTaskMapper.insertTaskConfigLineInfos(newTaskConfigLineInfoLists);
                log.info("[BatchNo:{}] 任务配置行信息同步完成，数量：{}", batchNo, newTaskConfigLineInfoLists.size());
            } else {
                log.info("[BatchNo:{}] 没有任务配置行信息需要同步", batchNo);
            }

            // 6. 同步任务定义信息
            log.info("[BatchNo:{}] 开始同步任务定义信息", batchNo);
            List<TempTaskDefinitionInfo> notExistTaskDefinitionInfoLists = exportTaskMapper.queryTaskDefinitionInfoByBatchNo(batchNo);
            if (null != notExistTaskDefinitionInfoLists && notExistTaskDefinitionInfoLists.size() > 0) {
                List<TempTaskDefinitionInfo> newTaskDefinitionInfoLists = initTaskDefinitionInfoLists(notExistTaskDefinitionInfoLists);
                exportTaskMapper.insertTaskDefinitionInfos(newTaskDefinitionInfoLists);
                log.info("[BatchNo:{}] 任务定义信息同步完成，数量：{}", batchNo, newTaskDefinitionInfoLists.size());
            } else {
                log.info("[BatchNo:{}] 没有任务定义信息需要同步", batchNo);
            }

            // 7. 同步任务版本信息
            log.info("[BatchNo:{}] 开始同步任务版本信息", batchNo);
            List<TempTaskVersionInfo> notExistTaskVersionInfoLists = exportTaskMapper.queryTaskVersionInfoByBatchNo(batchNo);
            if (null != notExistTaskVersionInfoLists && notExistTaskVersionInfoLists.size() > 0) {
                List<TempTaskVersionInfo> newTaskVersionInfoLists = initTaskVersionInfoLists(notExistTaskVersionInfoLists);
                exportTaskMapper.insertTaskVersionInfos(newTaskVersionInfoLists);
                log.info("[BatchNo:{}] 任务版本信息同步完成，数量：{}", batchNo, newTaskVersionInfoLists.size());
            } else {
                log.info("[BatchNo:{}] 没有任务版本信息需要同步", batchNo);
            }

            // 8. 同步di信息
            log.info("[BatchNo:{}] 开始同步di相关信息", batchNo);
            List<TempDiProcessDefinition> notExistDiProcessDefinitionLists = exportTaskMapper.queryDiProcessDefinitionByBatchNo(batchNo);
            if (null != notExistDiProcessDefinitionLists && notExistDiProcessDefinitionLists.size() > 0) {
                List<TempDiProcessDefinition> newDiProcessDefinitionLists = initDiProcessDefinitionLists(notExistDiProcessDefinitionLists);
                exportTaskMapper.insertDiProcessDefinitions(newDiProcessDefinitionLists);
            }
            List<TempDiProcessTaskRelation> notExistDiProcessTaskRelationLists = exportTaskMapper.queryDiProcessTaskRelationByBatchNo(batchNo);
            if (null != notExistDiProcessTaskRelationLists && notExistDiProcessTaskRelationLists.size() > 0) {
                List<TempDiProcessTaskRelation> newDiProcessTaskRelationLists = initDiProcessTaskRelationLists(notExistDiProcessTaskRelationLists);
                exportTaskMapper.insertDiProcessTaskRelations(newDiProcessTaskRelationLists);
            }
            List<TempDiScheduler> notExistDiSchedulerLists = exportTaskMapper.queryDiSchedulerByBatchNo(batchNo);
            if (null != notExistDiSchedulerLists && notExistDiSchedulerLists.size() > 0) {
                List<TempDiScheduler> newDiSchedulerLists = initDiSchedulerLists(notExistDiSchedulerLists);
                exportTaskMapper.insertDiSchedulers(newDiSchedulerLists);
            }

            // 9. 同步工作流定义（最重要的步骤）
            log.info("[BatchNo:{}] 开始同步工作流定义", batchNo);
            List<TempImport> notExistImportLists = exportTaskMapper.queryImportInfoByBatchNo(batchNo);
            if (null != notExistImportLists && notExistImportLists.size() > 0) {
                List<ProcessExportDTO> newImportLists = initImportLists(notExistImportLists);
                int successCount = 0;
                int failedCount = 0;

                for (ProcessExportDTO processExportDTO : newImportLists) {
                    try {
                        log.info("[BatchNo:{}] 开始导入工作流：{}", batchNo, processExportDTO.getProcessDefinition().getName());
                        processDefinitionService.importDataV2(processExportDTO, projectId);
                        successCount++;
                        log.info("[BatchNo:{}] 工作流导入成功：{}", batchNo, processExportDTO.getProcessDefinition().getName());
                    } catch (Exception e) {
                        failedCount++;
                        log.error("[BatchNo:{}] 工作流导入失败：{}，错误：{}", batchNo,
                                processExportDTO.getProcessDefinition().getName(), e.getMessage(), e);
                    }
                }

                log.info("[BatchNo:{}] 工作流定义同步完成，总数：{}，成功：{}，失败：{}",
                        batchNo, newImportLists.size(), successCount, failedCount);

                if (failedCount > 0) {
                    throw new RuntimeException(String.format("工作流导入部分失败，成功：%d，失败：%d", successCount, failedCount));
                }
            } else {
                log.info("[BatchNo:{}] 没有工作流定义需要同步", batchNo);
            }

            log.info("[BatchNo:{}] 所有数据同步完成", batchNo);

        } catch (Exception e) {
            log.error("[BatchNo:{}] 数据同步失败：{}", batchNo, e.getMessage(), e);
            throw new RuntimeException("数据同步失败", e);
        }
    }

    private void deleteTask(List<String> ids) {
        taskService.deleteBy(new InCondition("id", ids));
        List<TaskDefinitionInfoDTO> deleteTaskDefins = exportTaskMapper.queryTaskDefinitionInfoByTaskId(ids);
        exportTaskMapper.deleteTaskDefinitionInfoByTaskId(ids);
        if (null != deleteTaskDefins && deleteTaskDefins.size() > 0) {
            List<String> deleteVersionId = deleteTaskDefins.stream().map(t -> t.getCurrentVersionId()).collect(Collectors.toList());
            exportTaskMapper.deleteTaskConfigInfoByVersionIds(deleteVersionId);
            exportTaskMapper.deleteTaskConfigLineInfoByVersionIds(deleteVersionId);
            exportTaskMapper.deleteTaskVersionInfoByVersionIds(deleteVersionId);
        }
    }

    private void deleteProcess(String processDefinitionId) {
        exportTaskMapper.deleteDiProcessDefinitionByProcessDefinitionId(processDefinitionId);
        processDefinitionService.delete(processDefinitionId, new ProcessDefinition());
        exportTaskMapper.deleteDiProcessTaskRelationByProcessDefinitionId(processDefinitionId);
        exportTaskMapper.deleteDiSchedulerByProcessDefinitionId(processDefinitionId);
    }

    private List<ProcessExportDTO> initImportLists(List<TempImport> notExistImportLists) {
        List<ProcessExportDTO> list = new ArrayList<>();
        notExistImportLists.stream().forEach(importInfo -> {
            ProcessExportDTO tmp = JSONObject.parseObject(importInfo.getAfterJson(), ProcessExportDTO.class);
            list.add(tmp);
        });
        return list;
    }

    private List<TempTaskVersionInfo> initTaskVersionInfoLists(List<TempTaskVersionInfo> notExistTaskVersionInfoLists) {
        List<TempTaskVersionInfo> list = new ArrayList<>();
        notExistTaskVersionInfoLists.stream().forEach(taskVersionInfo -> {
            TempTaskVersionInfo tmp = JSONObject.parseObject(taskVersionInfo.getAfterJson(), TempTaskVersionInfo.class);
            list.add(tmp);
        });
        return list;
    }

    private List<TempDiProcessDefinition> initDiProcessDefinitionLists(List<TempDiProcessDefinition> notExistDiProcessDefinitionLists) {
        List<TempDiProcessDefinition> list = new ArrayList<>();
        notExistDiProcessDefinitionLists.stream().forEach(pd -> {
            TempDiProcessDefinition tmp = JSONObject.parseObject(pd.getAfterJson(), TempDiProcessDefinition.class);
            list.add(tmp);
        });
        return list;
    }

    private List<TempDiProcessTaskRelation> initDiProcessTaskRelationLists(List<TempDiProcessTaskRelation> notExistDiProcessTaskRelationLists) {
        List<TempDiProcessTaskRelation> list = new ArrayList<>();
        notExistDiProcessTaskRelationLists.stream().forEach(pt -> {
            TempDiProcessTaskRelation tmp = JSONObject.parseObject(pt.getAfterJson(), TempDiProcessTaskRelation.class);
            list.add(tmp);
        });
        return list;
    }

    private List<TempDiScheduler> initDiSchedulerLists(List<TempDiScheduler> notExistDiSchedulerLists) {
        List<TempDiScheduler> list = new ArrayList<>();
        notExistDiSchedulerLists.stream().forEach(scheduler -> {
            TempDiScheduler tmp = JSONObject.parseObject(scheduler.getAfterJson(), TempDiScheduler.class);
            list.add(tmp);
        });
        return list;
    }

    private List<TempTaskDefinitionInfo> initTaskDefinitionInfoLists(List<TempTaskDefinitionInfo> notExistTaskDefinitionInfoLists) {
        List<TempTaskDefinitionInfo> list = new ArrayList<>();
        notExistTaskDefinitionInfoLists.stream().forEach(taskDefinitionInfo -> {
            TempTaskDefinitionInfo tmp = JSONObject.parseObject(taskDefinitionInfo.getAfterJson(), TempTaskDefinitionInfo.class);
            list.add(tmp);
        });
        return list;
    }

    private List<TempTaskConfigLineInfo> initTaskConfigLineInfoLists(List<TempTaskConfigLineInfo> notExistTaskConfigLineInfoLists) {
        List<TempTaskConfigLineInfo> list = new ArrayList<>();
        notExistTaskConfigLineInfoLists.stream().forEach(taskConfigLineInfo -> {
            TempTaskConfigLineInfo tmp = JSONObject.parseObject(taskConfigLineInfo.getAfterJson(), TempTaskConfigLineInfo.class);
            tmp.setUpdateBy(tmp.getUpdateBy());
            list.add(tmp);
        });
        return list;
    }

    private List<TempTaskConfigInfo> initTaskConfigInfoLists(List<TempTaskConfigInfo> notExistTaskConfigInfoLists) {
        List<TempTaskConfigInfo> list = new ArrayList<>();
        notExistTaskConfigInfoLists.stream().forEach(taskConfigInfo -> {
            TempTaskConfigInfo tmp = JSONObject.parseObject(taskConfigInfo.getAfterJson(), TempTaskConfigInfo.class);
            list.add(tmp);
        });
        return list;
    }

    private List<TempTaskCatalogue> initTaskCatalogueLists(List<TempTaskCatalogue> notExistTaskConfigInfoLists) {
        List<TempTaskCatalogue> list = new ArrayList<>();
        notExistTaskConfigInfoLists.stream().forEach(taskConfigInfo -> {
            TempTaskCatalogue tmp = JSONObject.parseObject(taskConfigInfo.getAfterJson(), TempTaskCatalogue.class);
            list.add(tmp);
        });
        return list;
    }

    private List<TempDatasourceInfo> initDatasourceLists(List<TempDatasourceInfo> notExistLists) {
        List<TempDatasourceInfo> list = new ArrayList<>();
        notExistLists.stream().forEach(ds -> {
            TempDatasourceInfo tmp = JSONObject.parseObject(ds.getAfterJson(), TempDatasourceInfo.class);
            list.add(tmp);
        });
        return list;
    }

    private List<TempDatasourceInfoAuthorization> initDatasourceAuthorizationLists(List<TempDatasourceInfoAuthorization> notExistLists) {
        List<TempDatasourceInfoAuthorization> list = new ArrayList<>();
        notExistLists.stream().forEach(ds -> {
            TempDatasourceInfoAuthorization tmp = JSONObject.parseObject(ds.getAfterJson(), TempDatasourceInfoAuthorization.class);
            list.add(tmp);
        });
        return list;
    }

    private List<TempDatasourceInfoAuthorizationOperation> initDatasourceInfoAuthorizationOperationLists(List<TempDatasourceInfoAuthorizationOperation> notExistLists) {
        List<TempDatasourceInfoAuthorizationOperation> list = new ArrayList<>();
        notExistLists.stream().forEach(ds -> {
            TempDatasourceInfoAuthorizationOperation tmp = JSONObject.parseObject(ds.getAfterJson(), TempDatasourceInfoAuthorizationOperation.class);
            list.add(tmp);
        });
        return list;
    }

    private List<TempDatasourceReference> initDatasourceReferenceLists(List<TempDatasourceReference> notExistLists) {
        List<TempDatasourceReference> list = new ArrayList<>();
        notExistLists.stream().forEach(ds -> {
            TempDatasourceReference tmp = JSONObject.parseObject(ds.getAfterJson(), TempDatasourceReference.class);
            list.add(tmp);
        });
        return list;
    }

    private List<TempDatasourceBusiness> initDatasourceBusinessLists(List<TempDatasourceBusiness> notExistLists) {
        List<TempDatasourceBusiness> list = new ArrayList<>();
        notExistLists.stream().forEach(ds -> {
            TempDatasourceBusiness tmp = JSONObject.parseObject(ds.getAfterJson(), TempDatasourceBusiness.class);
            list.add(tmp);
        });
        return list;
    }

    public Tuple3<Integer, Integer, Integer> getTaskCount(String processDefinitionCodes) {
        List<String> codeList = Arrays.stream(processDefinitionCodes.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        AtomicInteger taskCount = new AtomicInteger();
        AtomicInteger datasourceCount = new AtomicInteger();
        codeList.stream().forEach(code -> {
            List<Task> tasks = taskService.getQuery().eq("processDefinitionCode", code).list();
            taskCount.addAndGet(tasks.size());
            //根据项目定义code查询到数据源id
            List<String> datasourceIds = getDatasourcesIds(tasks);
            datasourceCount.addAndGet(datasourceIds.stream().distinct().collect(Collectors.toList()).size());
        });
        return new Tuple3<>(codeList.size(), taskCount.get(), datasourceCount.get());
    }

    public Tuple3<Integer, Integer, Integer> getTaskCountFile(List<String> jsonFiles) {
        AtomicInteger workFlowCount = new AtomicInteger();
        AtomicInteger totalCount = new AtomicInteger();
        AtomicInteger datasourceCount = new AtomicInteger();
        for (String jsonFilePath : jsonFiles) {
            String importJson = "";
            File file = new File(jsonFilePath);
            try (InputStream inputStream = new FileInputStream(file)) {
                importJson = IOUtils.toString(inputStream, "UTF-8");
            } catch (IOException e) {
                throw new AppErrorException("读取JSON文件失败，原因：{}", e.getMessage());
            }
            try {
                IntegrationDTO integrationDTO = JSONObject.parseObject(importJson, IntegrationDTO.class);
                if (null != integrationDTO.getSccInfos()) {
                    List<ProcessExportDTO> processExportDTO = integrationDTO.getSccInfos();
                    processExportDTO.stream().forEach(t -> {
                        Tuple3<Integer, Integer, Integer> t3 = getTaskCount(t.getProcessDefinition().getCode());
                        workFlowCount.addAndGet(t3.getV1());
                        totalCount.addAndGet(t3.getV2());
                        datasourceCount.addAndGet(t3.getV3());
                    });
                }
            } catch (Exception e) {
                throw new AppErrorException("JSON文件导入到临时表失败，原因：{}", e.getMessage());
            }
        }
        return new Tuple3<>(workFlowCount.get(), totalCount.get(), datasourceCount.get());
    }
}
