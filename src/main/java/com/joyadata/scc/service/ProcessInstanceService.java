package com.joyadata.scc.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.joyadata.cores.kafka.start.interfaces.IJoyaKafkaProducer;
import com.joyadata.cores.kafka.start.util.KafkaClientUtil;
import com.joyadata.cores.kafka.start.util.constant.Topics;
import com.joyadata.exception.AppErrorException;
import com.joyadata.exception.AppWarningException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.interfaces.ISqlExecutor;
import com.joyadata.model.BaseBean;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.LeftJoinCondition;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.poi.excel.model.ExcelTitle;
import com.joyadata.poi.excel.util.JoyadataPoiUtil;
import com.joyadata.scc.dto.DsCatalogProcessDefinitionRelationDTO;
import com.joyadata.scc.enums.TaskExecutionStatus;
import com.joyadata.scc.enums.WorkflowExecutionStatus;
import com.joyadata.scc.model.*;
import com.joyadata.scc.model.dto.ExecuteParam;
import com.joyadata.scc.util.DataConverter;
import com.joyadata.scc.util.DateUtils;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.service.BaseService;
import com.joyadata.util.GeneratorUtil;
import com.joyadata.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ProcessInstanceService
 * @Description: TODO
 * @date 2024/4/17
 */
@Slf4j
@Service
public class ProcessInstanceService extends BaseService<ProcessInstance> {

    @Autowired
    private ProcessTaskRelationLogService processTaskRelationLogService;
    @Autowired
    private TaskLogService taskLogService;
    @Autowired
    HttpRequestFeignService httpRequestFeignService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    private static String INSTANCE_STATUS = "dedp:ds:clean:status";
    private static String INSTANCE_MAX_DAY = "dedp:ds:clean:day";
    private static String INSTANCE_TEST_MAX_DAY = "dedp:ds:cleantest:day";
    private static String INSTANCE_MAX_STRIP = "dedp:ds:clean:strip";
    private static String INSTANCE_MAX_QUEUE = "dedp:ds:clean:queue";
    @Autowired
    private StringRedisTemplate redisTemplate;

    private final IJoyaKafkaProducer<String, String> kafkaTemplate = KafkaClientUtil.getProducer(String.class, String.class);

    /**
     * 按照海豚生成规则组装对象
     * TODO 目前没有资源信息
     *
     * @param processDefinition
     */
    public void genDagData(ProcessDefinition processDefinition) {
        List<ProcessTaskRelation> taskRelations = this.findRelationByCode(processDefinition.getId(), processDefinition.getVersion());
        List<TaskLog> taskLogList = genTaskList(taskRelations);
        List<Task> tasks = new ArrayList<>();
        for (TaskLog log : taskLogList) {
            Task task = new Task();
            BeanUtils.copyProperties(log, task);
            tasks.add(task);
            task.setId(log.getTaskId());
        }

        processDefinition.setTaskRelationList(taskRelations);
        processDefinition.setTaskList(tasks);
    }

    //生成任务对象
    private List<TaskLog> genTaskList(List<ProcessTaskRelation> taskRelations) {
        Set<Task> taskSet = new HashSet<>();
        for (ProcessTaskRelation processTaskRelation : taskRelations) {
            if (Long.parseLong(processTaskRelation.getPreTaskCode()) > 0) {
                taskSet.add(new Task(processTaskRelation.getPreTaskCode(),
                        processTaskRelation.getPreTaskVersion()));
            }
            if (Long.parseLong(processTaskRelation.getPostTaskCode()) > 0) {
                taskSet.add(new Task(processTaskRelation.getPostTaskCode(),
                        processTaskRelation.getPostTaskVersion()));
            }
        }
        if (taskSet.isEmpty()) {
            return Lists.newArrayList();
        }
        List<TaskLog> result = new ArrayList<>();
        taskSet.forEach(t -> {
            TaskLog taskLog = taskLogService.getQuery().eq("taskId", t.getId()).eq("version", t.getVersion()).withs("catalogName").one();
            if (null != taskLog) {
                result.add(taskLog);
            }
        });
        return result;
    }

    //通过工作流id，工作流版本，获取工作流关系对象
    public List<ProcessTaskRelation> findRelationByCode(String processDefinitionId, int processDefinitionVersion) {
        List<ProcessTaskRelationLog> processTaskRelationLogList = processTaskRelationLogService.getQuery().eq("processDefinitionCode", processDefinitionId).eq("processDefinitionVersion", processDefinitionVersion).list();
        return processTaskRelationLogList.stream().map(r -> (ProcessTaskRelation) r).collect(Collectors.toList());
    }


    //通过工作流实例id获取任务实例情况
    public Map getTasksResponse(String projectId, String id) {
        Map<String, Object> headers = Utils.getHeader();
        String tasksUrl =
                dolphinscheduler + Constants.PROCESS_INSTANCES.replace("{projectCode}", projectId) + "/" + id + "/tasks";
        Map tasksResponse = httpRequestFeignService.get4url(tasksUrl, headers, Map.class);
        if (Integer.parseInt(String.valueOf(tasksResponse.get("code"))) != 0) {
            log.error("查看工作流实例失败,错误信息是 {}", tasksResponse);
            throw new AppErrorException("查看工作流实例失败！请联系管理员，报错信息：" + tasksResponse.get("msg").toString());
        }

        return tasksResponse;
    }

    public Response<?> saveStrategy(HttpServletRequest request, JSONObject body) {
        String projectId = request.getHeader("projectId");
        if (StringUtils.isBlank(projectId)) {
            log.error("projectId不能为空");
            throw new AppErrorException("projectId不能为空！");
        }
        String day = body.getString("day");
        String strip = body.getString("strip");
        String queue = body.getString("queue");
        String testDay = body.getString("testDay");
        log.info("项目id={}，工作流实例保存策略为：day={}，strip={}，testDay={}，queue={}", projectId, day, strip, testDay, queue);
        if (StringUtils.isNotBlank(day)) {
            redisTemplate.opsForHash().put(INSTANCE_MAX_DAY, projectId, day);
        } else {//为空就删除
            redisTemplate.opsForHash().delete(INSTANCE_MAX_DAY, projectId);
        }
        if (StringUtils.isNotBlank(strip)) {
            redisTemplate.opsForHash().put(INSTANCE_MAX_STRIP, projectId, strip);
        } else {//为空就删除
            redisTemplate.opsForHash().delete(INSTANCE_MAX_STRIP, projectId);
        }
        if (StringUtils.isNotBlank(queue)) {
            redisTemplate.opsForHash().put(INSTANCE_MAX_QUEUE, projectId, queue);
        } else {//为空就删除
            redisTemplate.opsForHash().delete(INSTANCE_MAX_QUEUE, projectId);
        }
        if (StringUtils.isNotBlank(testDay)) {
            redisTemplate.opsForHash().put(INSTANCE_TEST_MAX_DAY, projectId, testDay);
        } else {//为空就删除
            redisTemplate.opsForHash().delete(INSTANCE_TEST_MAX_DAY, projectId);
        }
        return ResponseFactory.makeSuccess("success");
    }

    public Response<?> saveStrategyOpenOrClose(HttpServletRequest request, JSONObject body) {
        String projectId = request.getHeader("projectId");
        if (StringUtils.isBlank(projectId)) {
            log.error("projectId不能为空");
            throw new AppErrorException("projectId不能为空！");
        }
        String status = body.getString("status");
        log.info("status={}", status);
        redisTemplate.opsForHash().put(INSTANCE_STATUS, projectId, status);
        if ("close".equals(status)) {//如果是关闭，就删除redis中的信息
            log.info("清除redis中的工作流实例存储策略。");
            redisTemplate.opsForHash().delete(INSTANCE_MAX_DAY, projectId);
            redisTemplate.opsForHash().delete(INSTANCE_MAX_STRIP, projectId);
            redisTemplate.opsForHash().delete(INSTANCE_TEST_MAX_DAY, projectId);
            //redisTemplate.opsForHash().delete(INSTANCE_MAX_QUEUE, projectId);
        }
        return ResponseFactory.makeSuccess("success");
    }

    public Response<?> getStrategy(String projectId) {
        Map<String, Object> result = new HashMap<>();
        Object isOpen = redisTemplate.opsForHash().get(INSTANCE_STATUS, projectId);
        result.put("status", null == isOpen ? "close" : isOpen);
        result.put("day", redisTemplate.opsForHash().get(INSTANCE_MAX_DAY, projectId));
        result.put("strip", redisTemplate.opsForHash().get(INSTANCE_MAX_STRIP, projectId));
        result.put("queue", redisTemplate.opsForHash().get(INSTANCE_MAX_QUEUE, projectId));
        result.put("testDay", redisTemplate.opsForHash().get(INSTANCE_TEST_MAX_DAY, projectId));
        return ResponseFactory.makeSuccess(result);
    }

    public void fixProcessInstanceCatalogId() {
        //给t_ds_catalog_process_definition_relation赋值
        ISqlExecutor sqlExecutor = this.getSqlExecutor();
        List<ProcessDefinition> processDefinitionList = sqlExecutor.excuteSelect("select id,catalog_id,catalog_parent_ids,project_id from business_scc.scc_process_definition where catalog_id is not null").list(ProcessDefinition.class);
        if (null != processDefinitionList && !processDefinitionList.isEmpty()) {
            processDefinitionList.forEach(t -> {
                String processDefinitionId = t.getId();
                String catalogId = t.getCatalogId();
                String catalogParentIds = t.getCatalogParentIds();
                String projectId = t.getProjectId();

                //修改目录挂接表
                findOrUpdateCatalogProcessDefinitionRelation(processDefinitionId, catalogId, catalogParentIds, projectId);
                //修改工作流实例表
                updateProcessInstance(processDefinitionId, catalogId, catalogParentIds);
            });
        }
    }

    private void updateProcessInstance(String processDefinitionId, String catalogId, String catalogParentIds) {
        ISqlExecutor sqlExecutor = this.getSqlExecutor();
        String updateSql = "UPDATE `business_ds`.`t_ds_process_instance` SET `catalog_id` = '%s',`catalog_parent_ids` = '%s' WHERE `process_definition_code` = %s";
        sqlExecutor.excuteUpdate(String.format(updateSql, catalogId, catalogParentIds, processDefinitionId));
    }

    private void findOrUpdateCatalogProcessDefinitionRelation(String processDefinitionId, String catalogId, String catalogParentIds, String projectId) {
        ISqlExecutor sqlExecutor = this.getSqlExecutor();
        String selectCatalogSql = "select * from `business_ds`.`t_ds_catalog_process_definition_relation` where process_definition_code=%s";
        DsCatalogProcessDefinitionRelationDTO catalogProcessDefinitionRelation = sqlExecutor.excuteSelect(String.format(selectCatalogSql, processDefinitionId)).one(DsCatalogProcessDefinitionRelationDTO.class);
        if (null == catalogProcessDefinitionRelation) {
            String insertSql = "INSERT INTO `business_ds`.`t_ds_catalog_process_definition_relation`(`id`, `catalog_id`, `catalog_parent_ids`, `process_definition_code`, `create_time`, `update_time`, `project_code`) VALUES ('%s', '%s', '%s', %s, '%s', '%s', '%s')";
            //新增
            sqlExecutor.excuteInsert(String.format(insertSql, GeneratorUtil.genCode(), catalogId, catalogParentIds, processDefinitionId, DateUtils.dateToString(new Date()), DateUtils.dateToString(new Date()), projectId));
        } else {
            //更新
            String updateSql = "UPDATE `business_ds`.`t_ds_catalog_process_definition_relation` SET `catalog_id` = '%s',`catalog_parent_ids` = '%s',`project_code` = '%s' WHERE `id` = '%s'";
            sqlExecutor.excuteUpdate(String.format(updateSql, catalogId, catalogParentIds, projectId, catalogProcessDefinitionRelation.getId()));
        }
    }

    public void forceProcessSuccess(String id, String projectId) {
        //查询该工作流实例下所有非成功状态的任务实例id，将其改为成功
        List<TaskInstance> taskInstanceList = getService(TaskInstance.class).getQuery().eq("processInstanceId", id)
                .notIn("state", TaskExecutionStatus.SUCCESS.getCode()).filters("id", "state", "taskId").list();
        ProcessInstance processInstance = this.getQuery().eq("id", id).filters("state", "processDefinitionId").one();
        ForceSuccessRecord forceSuccessRecord = new ForceSuccessRecord();
        forceSuccessRecord.setType(ForceSuccessRecord.Type.processInstance);
        forceSuccessRecord.setProjectId(projectId);
        forceSuccessRecord.setSourceState(WorkflowExecutionStatus.valueOf(processInstance.getState()).getCode());
        forceSuccessRecord.setTargetState(WorkflowExecutionStatus.SUCCESS.getCode());
        forceSuccessRecord.setProcessInstanceId(id);
        forceSuccessRecord.setProcessDefinitionId(processInstance.getProcessDefinitionId());
        //非成功状态的任务实例
        if (CollectionUtils.isNotEmpty(taskInstanceList)) {
            List<ForceSuccessRecord> forceSuccessRecordTaskList = new ArrayList<>();
            //将每一条任务实例的修改记录都存起来，将任务修改记录id放在工作流实例修改记录的forceSuccessRecordIds字段中
            for (TaskInstance taskInstance : taskInstanceList) {
                ForceSuccessRecord forceSuccessRecordTask = new ForceSuccessRecord();
                forceSuccessRecordTask.setId(GeneratorUtil.genCode());
                forceSuccessRecordTask.setType(ForceSuccessRecord.Type.taskInstance);
                forceSuccessRecordTask.setProjectId(projectId);
                forceSuccessRecordTask.setTaskId(taskInstance.getTaskId().toString());
                forceSuccessRecordTask.setTaskInstanceId(taskInstance.getId());
                forceSuccessRecordTask.setSourceState(TaskExecutionStatus.valueOf(taskInstance.getState()).getCode());
                forceSuccessRecordTask.setTargetState(TaskExecutionStatus.SUCCESS.getCode());
                forceSuccessRecordTask.setProcessInstanceId(id);
                forceSuccessRecordTask.setProcessDefinitionId(processInstance.getProcessDefinitionId());
                forceSuccessRecordTaskList.add(forceSuccessRecordTask);
            }
            String forceSuccessRecordTaskIds = forceSuccessRecordTaskList.stream()
                    .map(BaseBean::getId)
                    .collect(Collectors.joining(","));
            forceSuccessRecord.setForceSuccessRecordIds(forceSuccessRecordTaskIds);
            getService(ForceSuccessRecord.class).add(forceSuccessRecordTaskList);
            String taskInstanceIds = taskInstanceList.stream()
                    .map(s -> "'" + s.getId() + "'")
                    .collect(Collectors.joining(","));
            //修改scc库、ds库中任务实例数据的状态
            updateTaskInstanceState(WorkflowExecutionStatus.SUCCESS.getCode(), taskInstanceIds);
        }
        //修改scc库、ds库中工作流实例数据的状态
        updateProcessInstanceState(id, WorkflowExecutionStatus.SUCCESS.getCode());
        getService(ForceSuccessRecord.class).add(forceSuccessRecord);
        // 发送告警恢复--浙江农信
        sendAlarm(id, projectId, processInstance.getProcessDefinitionId());
    }

    private void updateTaskInstanceState(int state, String taskInstanceIds) {
        ISqlExecutor sqlExecutor = getSqlExecutor();
        String updateDsTaskInstanceSql = "UPDATE `business_ds`.`t_ds_task_instance` SET `state` = %s WHERE `id` in(%s)";
        sqlExecutor.excuteUpdate(String.format(updateDsTaskInstanceSql, state, taskInstanceIds));
//        String updateSccTaskInstanceSql = "UPDATE `business_scc`.`scc_task_instance` SET `state` = %s WHERE `id` in(%s)";
//        sqlExecutor.excuteUpdate(String.format(updateSccTaskInstanceSql, state, taskInstanceIds));
    }

    private void updateProcessInstanceState(String id, int state) {
        ISqlExecutor sqlExecutor = getSqlExecutor();
        String updateDsProcessInstanceSql = "UPDATE `business_ds`.`t_ds_process_instance` SET `state` = %s WHERE `id` = %s";
        sqlExecutor.excuteUpdate(String.format(updateDsProcessInstanceSql, state, id));
//        String updateSccProcessInstanceSql = "UPDATE `business_scc`.`scc_process_instance` SET `state` = %s WHERE `id` = %s";
//        sqlExecutor.excuteUpdate(String.format(updateSccProcessInstanceSql, state, id));

    }


    public JSONObject getRunningState(String projectId, String startDate, String endDate, Integer type) {
        JSONObject result;
        if (type == 0) {//0是工作流
            String sql = String.format(" SELECT\n" +
                    "  IFNULL(SUM(CASE WHEN state = 6 THEN 1 ELSE 0 END), 0) AS failureNum,\n" +
                    "  IFNULL(SUM(CASE WHEN state = 7 THEN 1 ELSE 0 END), 0) AS successNum,\n" +
                    "  IFNULL(SUM(CASE WHEN state = 1 THEN 1 ELSE 0 END), 0) AS runningNum,\n" +
                    "  IFNULL(SUM(CASE WHEN state = 0 THEN 1 ELSE 0 END), 0) AS submitSuccessNum,\n" +
                    "  IFNULL(SUM(CASE WHEN state IN (2,3,4,5,14) THEN 1 ELSE 0 END), 0) AS blockNum\n" +
                    "FROM scc_process_instance\n" +
                    "WHERE project_id = '%s'\n" +
                    "  AND start_time >= '%s'\n" +
                    "  AND start_time < '%s';", projectId, startDate, endDate);
            result = this.getSqlExecutor().excuteSelect(sql).one(JSONObject.class);
        } else if (type == 1) {//1是任务
            String sql = String.format(" SELECT\n" +
                    "  IFNULL(SUM(CASE WHEN state = 6 THEN 1 ELSE 0 END), 0) AS failureNum,\n" +
                    "  IFNULL(SUM(CASE WHEN state = 7 THEN 1 ELSE 0 END), 0) AS successNum,\n" +
                    "  IFNULL(SUM(CASE WHEN state = 1 THEN 1 ELSE 0 END), 0) AS runningNum,\n" +
                    "  IFNULL(SUM(CASE WHEN state = 0 THEN 1 ELSE 0 END), 0) AS submitSuccessNum,\n" +
                    "  IFNULL(SUM(CASE WHEN state IN (3,9,12,17) THEN 1 ELSE 0 END), 0) AS blockNum\n" +
                    "FROM scc_task_instance\n" +
                    "WHERE project_id = '%s'\n" +
                    "  AND start_time >= '%s'\n" +
                    "  AND start_time < '%s';", projectId, startDate, endDate);
            result = this.getSqlExecutor().excuteSelect(sql).one(JSONObject.class);
        } else {
            throw new AppErrorException("未知类型！");
        }
        return result;
    }

    public void exportXlsx(HttpServletRequest request, HttpServletResponse response, List<ProcessInstance> processInstanceList) throws IOException {
        if (org.springframework.util.CollectionUtils.isEmpty(processInstanceList))
            processInstanceList = new ArrayList<>();
        List<JSONObject> jsonObjectList = JsonUtil.toBeanList(processInstanceList, JSONObject.class);
        String language = request.getHeader("accept-language");
        //数据转换
        dataTransition(jsonObjectList, language);
        String fileName = "ProcessInstance.xlsx";
        String sheetName = "sheet";
        List<ExcelTitle> title = getTitle();
        JoyadataPoiUtil.downloadExcel(request, response, fileName, sheetName, title, jsonObjectList);
    }

    private void dataTransition(List<JSONObject> jsonObjectList, String language) {
        DataConverter dataConverter = new DataConverter(language);
        String format = "yyyy-MM-dd HH:mm:ss";
        String timezone = "Asia/Shanghai";
        jsonObjectList.forEach(jsonObject -> {
            //处理存在中英文转换问题的字段
            String state = dataConverter.convertProcessInstanceState(jsonObject.getString("state"));
            String commandType = dataConverter.convertCommandType(jsonObject.getString("commandType"));
            jsonObject.put("state", state);
            jsonObject.put("commandType", commandType);
            //处理时间字段，转为yyyy-MM-dd HH:mm:ss格式
            Date scheduleTime = jsonObject.getDate("scheduleTime");
            if (null != scheduleTime) {
                jsonObject.put("scheduleTime", DateUtils.format(scheduleTime, format, timezone));
            }
            Date endTime = jsonObject.getDate("endTime");
            if (null != endTime) {
                jsonObject.put("endTime", DateUtils.format(endTime, format, timezone));
            }
            Date startTime = jsonObject.getDate("startTime");
            if (null != startTime) {
                jsonObject.put("startTime", DateUtils.format(startTime, format, timezone));
            }
            //取出全局参数
            JSONArray globalParams = jsonObject.getJSONArray("globalParams");
            if (null != globalParams) {
                for (Object globalParam : globalParams) {
                    JSONObject globalParamJson = JsonUtil.toJSON(globalParam);
                    if (null == globalParamJson) continue;
                    String prop = globalParamJson.getString("prop");
                    if (null != prop && ("ac_date_begin".equals(prop) || "ac_date_end".equals(prop))) {
                        jsonObject.put(prop, globalParamJson.get("value"));
                    }
                }
            }
            jsonObject.put("dryRun", jsonObject.getIntValue("dryRun") == 0 ? "NO" : "YES");
        });
    }

    private List<ExcelTitle> getTitle() {
        List<ExcelTitle> title = new ArrayList<>();
        ExcelTitle title1 = new ExcelTitle();
        title1.setName("工作流实例名称");
        title1.setCode("name");
        title1.setPos(1);
        ExcelTitle title2 = new ExcelTitle();
        title2.setName("状态");
        title2.setCode("state");
        title2.setPos(2);
        ExcelTitle title3 = new ExcelTitle();
        title3.setName("任务数量");
        title3.setCode("taskNum");
        title3.setPos(3);
        ExcelTitle title4 = new ExcelTitle();
        title4.setName("运行类型");
        title4.setCode("commandType");
        title4.setPos(4);
        ExcelTitle title5 = new ExcelTitle();
        title5.setName("调度时间");
        title5.setCode("scheduleTime");
        title5.setPos(5);
        ExcelTitle title6 = new ExcelTitle();
        title6.setName("开始时间");
        title6.setCode("startTime");
        title6.setPos(6);
        ExcelTitle title7 = new ExcelTitle();
        title7.setName("结束时间");
        title7.setCode("endTime");
        title7.setPos(7);
        ExcelTitle title8 = new ExcelTitle();
        title8.setName("运行时长");
        title8.setCode("duration");
        title8.setPos(8);
        ExcelTitle title9 = new ExcelTitle();
        title9.setName("运行次数");
        title9.setCode("runTimes");
        title9.setPos(9);
        ExcelTitle title10 = new ExcelTitle();
        title10.setName("空跑标识");
        title10.setCode("dryRun");
        title10.setPos(10);
        ExcelTitle title11 = new ExcelTitle();
        title11.setName("容错标识");
        title11.setCode("recovery");
        title11.setPos(11);
        ExcelTitle title12 = new ExcelTitle();
        title12.setName("执行人");
        title12.setCode("executorName");
        title12.setPos(12);
        ExcelTitle title13 = new ExcelTitle();
        title13.setName("会计日期");
        title13.setCode("acDate");
        title13.setPos(13);
        ExcelTitle title14 = new ExcelTitle();
        title14.setName("数据开始时间");
        title14.setCode("ac_date_begin");
        title14.setPos(14);
        ExcelTitle title15 = new ExcelTitle();
        title15.setName("数据结束时间");
        title15.setCode("ac_date_end");
        title15.setPos(15);
        ExcelTitle title16 = new ExcelTitle();
        title16.setName("主机");
        title16.setCode("host");
        title16.setPos(16);
        title.add(title1);
        title.add(title2);
        title.add(title3);
        title.add(title4);
        title.add(title5);
        title.add(title6);
        title.add(title7);
        title.add(title8);
        title.add(title9);
        title.add(title10);
        title.add(title11);
        title.add(title12);
        title.add(title13);
        title.add(title14);
        title.add(title15);
        title.add(title16);
        return title;
    }

    private void sendAlarm(String processInstanceId, String projectId, String processDefinitionId) {
        new Thread(() -> {
            try {
                ProcessDefinition processDefinition = getService(ProcessDefinition.class).getById(processDefinitionId);
                JSONObject data = new JSONObject();
                data.put("processDefinitionName", processDefinition.getName());
                data.put("processDefinitionId", processDefinition.getId());
                data.put("processInstanceId", processInstanceId);
                data.put("businessId", processDefinition.getId());
                data.put("projectId", projectId);
                Map<String, Object> map = new HashMap<>();
                map.put("eventCode", "B301015");
                map.put("data", data);
                map.put("tenantCode", processDefinition.getTenantCode());
                log.info("发送告警=B301015, map={}", map);
                kafkaTemplate.send(Topics.APP_EVENTS_V1_R2P1, JSONObject.toJSONString(map));
            } catch (Exception e) {
                log.error("告警发送失败, processInstanceId={}, projectId={}, error={}",
                        processInstanceId, projectId, e.getMessage(), e);
            }
        }).start();
    }

    public String stop(String projectId, String id, String state) {
        /**
         *   READY_STOP: '准备停止',
         *   STOP: '停止',
         *   FAILURE: '失败',
         *   SUCCESS: '成功',
         *   PAUSE: '暂停'
         *   这些状态不允洗停止
         */
        if (WorkflowExecutionStatus.READY_STOP.name().equals(state)) {
            return "工作流实例状态为“停止”，不允许停止";
        } else if (WorkflowExecutionStatus.STOP.name().equals(state)) {
            return "工作流实例状态为“准备停止”，不允许停止";
        } else if (WorkflowExecutionStatus.FAILURE.name().equals(state)) {
            return "工作流实例状态为“失败”，不允许停止";
        } else if (WorkflowExecutionStatus.SUCCESS.name().equals(state)) {
            return "工作流实例状态为“成功”，不允许停止";
        } else if (WorkflowExecutionStatus.PAUSE.name().equals(state)) {
            return "工作流实例状态为“暂停”，不允许停止";
        }
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + "/projects/" + projectId + "/executors/execute";
        ExecuteParam param = new ExecuteParam();
        param.setProcessInstanceId(id);
        param.setProjectId(projectId);
        param.setExecuteType("STOP");
        Map result = Utils.webClientPost(url, JsonUtil.toMap(param, Map.class), headers.get(Constants.SESSION_ID).toString());
        if (Integer.parseInt(String.valueOf(result.get("code"))) == 0 || result.get("msg").toString().equals("execute process instance error") || Integer.parseInt(String.valueOf(result.get("code"))) == 50006) {
            //终止如果返回工作流错误 其实也是停止掉了返回成功状态
            return "success";
        }
        return result.get("msg").toString();
    }
}
